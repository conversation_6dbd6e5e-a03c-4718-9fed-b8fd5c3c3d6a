import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../services/pocketbase_service.dart';
import '../../models/models.dart';

class AddAppointmentScreen extends StatefulWidget {
  const AddAppointmentScreen({super.key});

  @override
  State<AddAppointmentScreen> createState() => _AddAppointmentScreenState();
}

class _AddAppointmentScreenState extends State<AddAppointmentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _regionController = TextEditingController();
  final _buildingController = TextEditingController();
  
  AppointmentPrivacy _privacy = AppointmentPrivacy.private;
  DateTime? _selectedDate;
  String? _selectedTime;
  List<User> _selectedGuests = [];
  bool _isLoading = false;

  @override
  void dispose() {
    _titleController.dispose();
    _regionController.dispose();
    _buildingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة موعد جديد'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _handleSubmit,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('حفظ'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Title Field
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'موضوع الموعد *',
                hintText: 'أدخل موضوع الموعد',
                prefixIcon: Icon(Icons.title),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال موضوع الموعد';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Privacy Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الخصوصية *',
                      style: AppTheme.labelLarge,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: RadioListTile<AppointmentPrivacy>(
                            title: const Text('عام'),
                            value: AppointmentPrivacy.public,
                            groupValue: _privacy,
                            onChanged: (value) {
                              setState(() {
                                _privacy = value!;
                              });
                            },
                          ),
                        ),
                        Expanded(
                          child: RadioListTile<AppointmentPrivacy>(
                            title: const Text('خاص'),
                            value: AppointmentPrivacy.private,
                            groupValue: _privacy,
                            onChanged: (value) {
                              setState(() {
                                _privacy = value!;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Region Field
            TextFormField(
              controller: _regionController,
              decoration: const InputDecoration(
                labelText: 'المنطقة *',
                hintText: 'أدخل المنطقة',
                prefixIcon: Icon(Icons.location_on),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال المنطقة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Building Field (Optional)
            TextFormField(
              controller: _buildingController,
              decoration: const InputDecoration(
                labelText: 'المبنى (اختياري)',
                hintText: 'أدخل اسم المبنى',
                prefixIcon: Icon(Icons.business),
              ),
            ),
            const SizedBox(height: 16),

            // Date Selection
            Card(
              child: ListTile(
                leading: const Icon(Icons.calendar_today),
                title: const Text('التاريخ *'),
                subtitle: Text(
                  _selectedDate != null
                      ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
                      : 'اختر التاريخ',
                ),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: _selectDate,
              ),
            ),
            const SizedBox(height: 16),

            // Time Selection
            Card(
              child: ListTile(
                leading: const Icon(Icons.access_time),
                title: const Text('الوقت *'),
                subtitle: Text(_selectedTime ?? 'اختر الوقت'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: _selectTime,
              ),
            ),
            const SizedBox(height: 16),

            // Guests Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'المدعوين',
                          style: AppTheme.labelLarge,
                        ),
                        const Spacer(),
                        TextButton.icon(
                          onPressed: _addGuests,
                          icon: const Icon(Icons.person_add),
                          label: const Text('إضافة'),
                        ),
                      ],
                    ),
                    if (_selectedGuests.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: _selectedGuests.map((guest) {
                          return Chip(
                            avatar: CircleAvatar(
                              backgroundColor: AppTheme.primaryColor,
                              child: Text(
                                guest.username[0].toUpperCase(),
                                style: const TextStyle(color: Colors.white, fontSize: 12),
                              ),
                            ),
                            label: Text(guest.username),
                            deleteIcon: const Icon(Icons.close, size: 18),
                            onDeleted: () {
                              setState(() {
                                _selectedGuests.remove(guest);
                              });
                            },
                          );
                        }).toList(),
                      ),
                    ] else
                      Text(
                        'لم يتم إضافة مدعوين',
                        style: AppTheme.bodyMedium.copyWith(color: AppTheme.textHint),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 32),

            // Submit Button
            ElevatedButton(
              onPressed: _isLoading ? null : _handleSubmit,
              child: _isLoading
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('جاري الحفظ...'),
                      ],
                    )
                  : const Text('إنشاء الموعد'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _selectTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (time != null) {
      setState(() {
        _selectedTime = '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
      });
    }
  }

  void _addGuests() {
    // TODO: Implement guest selection dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة نافذة اختيار المدعوين قريباً')),
    );
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار التاريخ')),
      );
      return;
    }

    if (_selectedTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار الوقت')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final appointment = await PocketbaseService.instance.createAppointment(
        title: _titleController.text.trim(),
        privacy: _privacy.toString(),
        region: _regionController.text.trim(),
        building: _buildingController.text.trim().isNotEmpty 
            ? _buildingController.text.trim() 
            : null,
        datetime: _selectedDate!,
        time: _selectedTime!,
        guestIds: _selectedGuests.map((guest) => guest.id).toList(),
      );

      if (appointment != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إنشاء الموعد بنجاح')),
          );
          context.go(AppConstants.homeRoute);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء الموعد: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
