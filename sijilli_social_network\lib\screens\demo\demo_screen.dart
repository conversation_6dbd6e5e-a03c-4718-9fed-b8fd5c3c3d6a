import 'package:flutter/material.dart';
import '../../widgets/appointment_card.dart';
import '../../utils/sample_data.dart';
import '../../constants/app_theme.dart';

class DemoScreen extends StatelessWidget {
  const DemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('عرض تجريبي - بطاقة الموعد'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          const Text(
            'تصميم بطاقة الموعد',
            style: AppTheme.headlineLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'مطابق للتصميم المطلوب في PRD',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          
          // Sample appointment card
          AppointmentCard(
            appointment: SampleData.sampleAppointment,
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم النقر على البطاقة')),
              );
            },
            onCopy: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم نسخ بيانات الموعد')),
              );
            },
          ),
          
          const SizedBox(height: 24),
          
          // Features list
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الميزات المطبقة:',
                    style: AppTheme.headlineSmall,
                  ),
                  const SizedBox(height: 12),
                  _buildFeatureItem('✅ تصميم بنسبة 4:1'),
                  _buildFeatureItem('✅ صورة المالك واسمه'),
                  _buildFeatureItem('✅ عداد الأيام المتبقية مع الألوان'),
                  _buildFeatureItem('✅ العنوان في الوسط'),
                  _buildFeatureItem('✅ تفاصيل المكان والتاريخ والوقت'),
                  _buildFeatureItem('✅ زر النسخ'),
                  _buildFeatureItem('✅ صورة المدعو الأول مع مؤشر الحالة'),
                  _buildFeatureItem('✅ دعم RTL للنصوص العربية'),
                  _buildFeatureItem('✅ ألوان متناسقة مع الثيم'),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // More sample cards
          const Text(
            'أمثلة أخرى:',
            style: AppTheme.headlineSmall,
          ),
          const SizedBox(height: 12),
          
          ...SampleData.sampleAppointments.skip(1).map((appointment) => 
            AppointmentCard(
              appointment: appointment,
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('تم النقر على: ${appointment.title}')),
                );
              },
              onCopy: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تم نسخ بيانات الموعد')),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Text(
        text,
        style: AppTheme.bodyMedium,
      ),
    );
  }
}
