/// <reference path="../pb_data/types.d.ts" />
migrate((app) => {
  const collection = app.findCollectionByNameOrId("pbc_3414089001")

  // update field
  collection.fields.addAt(10, new Field({
    "hidden": false,
    "id": "date2618022606",
    "max": "",
    "min": "",
    "name": "date",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "date"
  }))

  return app.save(collection)
}, (app) => {
  const collection = app.findCollectionByNameOrId("pbc_3414089001")

  // update field
  collection.fields.addAt(10, new Field({
    "hidden": false,
    "id": "date2618022606",
    "max": "",
    "min": "",
    "name": "DATE",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "date"
  }))

  return app.save(collection)
})
