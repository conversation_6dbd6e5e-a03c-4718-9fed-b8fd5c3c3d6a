import '../models/models.dart';

class SampleData {
  static User get sampleOwner => User(
    id: 'owner123',
    username: 'school_principal',
    email: '<EMAIL>',
    emailVisibility: false,
    verified: true,
    role: UserRole.admin,
    created: DateTime.now().subtract(const Duration(days: 30)),
    updated: DateTime.now(),
    displayName: 'السيد مدير المدرسة',
  );

  static User get sampleGuest => User(
    id: 'guest123',
    username: 'teacher_ahmed',
    email: '<EMAIL>',
    emailVisibility: false,
    verified: true,
    role: UserRole.user,
    created: DateTime.now().subtract(const Duration(days: 15)),
    updated: DateTime.now(),
    displayName: 'الأستاذ أحمد',
  );

  static Appointment get sampleAppointment => Appointment(
    id: 'appointment123',
    title: 'اجتماع الكادر التعليمي السنوي',
    privacy: AppointmentPrivacy.public,
    region: 'النعيم',
    building: 'مدرسة حمدان',
    datetime: DateTime(2036, 6, 22), // السبت 22 يونيو 2036
    time: '21:00', // 9:00 مساء
    owner: sampleOwner,
    guests: [sampleGuest],
    created: DateTime.now().subtract(const Duration(hours: 2)),
    updated: DateTime.now(),
  );

  static List<Appointment> get sampleAppointments => [
    sampleAppointment,
    Appointment(
      id: 'appointment124',
      title: 'ورشة تطوير المناهج',
      privacy: AppointmentPrivacy.private,
      region: 'الفروانية',
      building: 'مركز التدريب',
      datetime: DateTime.now().add(const Duration(days: 7)),
      time: '14:00',
      owner: sampleOwner,
      guests: [sampleGuest],
      created: DateTime.now().subtract(const Duration(hours: 5)),
      updated: DateTime.now(),
    ),
    Appointment(
      id: 'appointment125',
      title: 'اجتماع أولياء الأمور',
      privacy: AppointmentPrivacy.public,
      region: 'حولي',
      building: null,
      datetime: DateTime.now().add(const Duration(days: 2)),
      time: '18:30',
      owner: sampleOwner,
      guests: [],
      created: DateTime.now().subtract(const Duration(hours: 1)),
      updated: DateTime.now(),
    ),
  ];
}
