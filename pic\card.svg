<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="240.000mm" height="74.724mm" viewBox="157.000000 -567.000000 240.000000 74.724365">
    <rect transform="matrix(1,0,0,-1,277,-529.637817)" style="stroke:#4a6fe3;stroke-width:0.050000mm;fill:none" x="-120" y="-37.362198" width="240" height="74.724396" rx="10"/>
    <rect transform="matrix(1,0,0,-1,277,-522.126282)" style="stroke:#b45a00;stroke-width:0.050000mm;fill:none" x="-117.403" y="-27.151495" width="234.806" height="54.30299" rx="8"/>
    <rect transform="matrix(1,0,0,-1,261.017944,-533.399414)" style="stroke:#000000;stroke-width:0.050000mm;fill:none" x="-94.982071" y="-9.100641" width="189.964142" height="18.201283"/>
    <rect transform="matrix(1,0,0,-1,283.212067,-517.002136)" style="stroke:#000000;stroke-width:0.050000mm;fill:none" x="-72.787941" y="-5.509249" width="145.575882" height="11.018498"/>
    <rect transform="matrix(1,0,0,-1,283.212067,-504.435577)" style="stroke:#000000;stroke-width:0.050000mm;fill:none" x="-72.787941" y="-5.575395" width="145.575882" height="11.150789"/>
    <rect transform="matrix(1,0,0,-1,186.993668,-510.26593)" style="stroke:#000000;stroke-width:0.050000mm;fill:none" x="-20.95779" y="-11.405827" width="41.915581" height="22.811653" rx="3"/>
    <rect transform="matrix(1.368441,0,0,-1.368455,359.781799,-558.276245)" style="stroke:#000000;stroke-width:0.036538mm;fill:none" x="-21.409224" y="-4.190517" width="42.818447" height="8.381035" rx="1"/>
    <ellipse transform="matrix(1,0,0,-1,374,-527.501831)" style="stroke:#000000;stroke-width:0.050000mm;fill:none" rx="13.6377" ry="13.564528" cx="0" cy="0"/>
    <rect transform="matrix(1,0,0,-1,209.979172,-558.276367)" style="stroke:#000000;stroke-width:0.050000mm;fill:none" x="-29.297243" y="-5.734473" width="58.594486" height="11.468946" rx="1"/>
    <ellipse transform="matrix(0.397903,0,0,-0.400048,172.035873,-558.276245)" style="stroke:#000000;stroke-width:0.125659mm;fill:none" rx="13.6377" ry="13.564528" cx="0" cy="0"/>
    <path transform="matrix(1,0,0,-1,0,0)" style="stroke:none;fill:#ff0000;fill-rule:evenodd" d="M178.226303,558.276245C178.226303,554.857361,175.454758,552.085815,172.035873,552.085815C168.616989,552.085815,165.845444,554.857361,165.845444,558.276245C165.845444,561.695129,168.616989,564.466675,172.035873,564.466675C175.454758,564.466675,178.226303,561.695129,178.226303,558.276245zM177.959579,558.276245C177.959579,555.0047,175.307449,552.352539,172.035873,552.352539C168.764297,552.352539,166.112167,555.0047,166.112167,558.276245C166.112167,561.547791,168.764297,564.199951,172.035873,564.199951C175.307449,564.199951,177.959579,561.547791,177.959579,558.276245zM174.029282,555.959961L171.071274,555.959961L171.210922,556.161133C171.42836,556.418884,171.671295,556.653931,171.93602,556.862793L172.432114,557.284668C172.89978,557.654175,173.317825,558.08252,173.675766,558.559082C174.155334,559.197205,174.138,560.080444,173.633774,560.699219C173.251938,561.114624,172.703934,561.336853,172.14061,561.304688C171.585709,561.340515,171.041916,561.134583,170.649887,560.740234C170.368683,560.408813,170.204498,559.994141,170.182602,559.560059L170.943344,559.481934L170.965317,559.750488C171.04512,560.319458,171.55127,560.730347,172.124496,560.691406C172.650925,560.726807,173.124847,560.372742,173.240219,559.85791C173.291229,559.460999,173.173096,559.061279,172.914536,558.755859C172.520554,558.281677,172.074753,557.853027,171.585434,557.478027L171.287094,557.226563C170.84874,556.882751,170.484467,556.453674,170.216293,555.965332C170.096558,555.745605,170.036606,555.49823,170.042465,555.248047L174.029282,555.248047L174.029282,555.959961z"/>
    <path transform="matrix(1,0,0,-1,0,0)" style="stroke:none;fill:#ff0000;fill-rule:evenodd" d="M375.11084,530.530151L374.633789,530.530151L374.52002,530.332886C374.100647,529.759033,373.538452,529.305115,372.88916,529.015991L372.88916,528.300171C373.43161,528.508423,373.933014,528.810486,374.370605,529.192749L374.370605,524.473511L375.11084,524.473511L375.11084,530.530151z"/>
    <path transform="matrix(1,0,0,-1,0,0)" style="stroke:none;fill:#ff0000;fill-rule:evenodd" d="M359.362183,554.807312L359.445709,555.386658C359.886688,557.674194,360.924011,559.803894,362.453217,561.561401L362.453217,562.349854L357.110413,562.349854L357.110413,561.37561L361.152924,561.37561L360.772736,560.911255C359.296265,558.969666,358.42392,556.636597,358.264374,554.202637L359.306061,554.202637L359.362183,554.807312z"/>
    <path transform="matrix(1,0,0,-1,0,0)" style="stroke:none;fill:#ff0000;fill-rule:evenodd" d="M187.357834,507.201538C188.052689,507.288269,188.633667,507.772125,188.84465,508.439819C189.081375,509.072083,189.02446,509.777039,188.689377,510.363159C188.213577,511.15155,187.204819,511.432404,186.39006,511.003296C186.122742,510.86264,185.897095,510.654175,185.735764,510.398804C185.720474,510.872528,185.791626,511.345123,185.945724,511.793335C186.069504,512.143677,186.314453,512.438477,186.636154,512.62439C187.09053,512.861877,187.650421,512.72522,187.94426,512.305054C188.036209,512.143127,188.101807,511.96759,188.138596,511.785034L188.875412,511.842651C188.837891,512.265442,188.64328,512.658813,188.330002,512.94519C187.872894,513.304871,187.276688,513.435974,186.710861,513.301147C186.107651,513.17627,185.603821,512.763489,185.362717,512.196655C185.093811,511.535339,184.969925,510.824005,184.999435,510.110718C184.971359,509.477661,185.083588,508.846222,185.328049,508.261597C185.699509,507.502136,186.522308,507.072418,187.357834,507.201538zM186.922775,507.805542C186.452789,507.884064,186.070541,508.227844,185.942795,508.68689C185.74556,509.196198,185.844864,509.772888,186.201096,510.18689C186.600082,510.600922,187.238693,510.667267,187.714279,510.344116C187.988571,510.142609,188.166306,509.835663,188.204514,509.497437C188.268448,509.116058,188.209274,508.724274,188.035568,508.378784C187.82341,507.971588,187.377502,507.741882,186.922775,507.805542z"/>
    <path transform="matrix(1,0,0,-1,0,0)" style="stroke:none;fill:#ff0000;fill-rule:evenodd" d="M210.198685,555.199402C211.184113,555.224792,211.969666,556.031982,211.968216,557.017761C211.99469,557.734253,211.524002,558.375305,210.832474,558.564636L211.042435,558.657898C211.567413,558.924866,211.837082,559.520386,211.69136,560.091003C211.526154,560.808289,210.896896,561.32312,210.161087,561.342957C209.580765,561.423279,208.997421,561.215454,208.598587,560.786316C208.172913,560.300415,208.116013,559.593811,208.45845,559.046082C208.63176,558.819763,208.870117,558.651733,209.141556,558.564636L208.88179,558.476746C208.402206,558.279663,208.06868,557.835938,208.01265,557.320496C207.933777,556.836914,208.052353,556.341675,208.342819,555.947083C208.633286,555.552551,209.070938,555.292175,209.556107,555.223816C209.768646,555.190979,209.984268,555.1828,210.198685,555.199402zM209.775345,558.865417C209.385742,558.92511,209.077881,559.227051,209.010696,559.615417C208.917053,560.062378,209.162476,560.511169,209.58931,560.673523C210.091888,560.880798,210.668549,560.650146,210.889603,560.153503C211.068527,559.735352,210.92099,559.248596,210.539993,559.000183C210.310516,558.862366,210.038116,558.814331,209.775345,558.865417zM209.821243,555.810242C209.258438,555.865234,208.813919,556.313843,208.764114,556.877136C208.666794,557.508179,209.087021,558.103149,209.71431,558.222351C210.210709,558.327881,210.722229,558.124878,211.011185,557.707703C211.332123,557.215027,211.271866,556.566467,210.865677,556.141296C210.586395,555.877014,210.201782,555.755127,209.821243,555.810242z"/>
    <path transform="matrix(1,0,0,-1,0,0)" style="stroke:none;fill:#ff0000;fill-rule:evenodd" d="M287.17804,501.420258C288.176758,501.529572,288.945404,502.351501,288.98761,503.355316C289.103027,504.207458,288.636475,505.033081,287.846985,505.373871C287.224487,505.621246,286.518341,505.529846,285.979309,505.132172L286.304504,506.753265L288.711243,506.753265L288.711243,507.461273L285.711731,507.461273L285.127747,504.363129L285.823059,504.272308L285.912903,504.395844C286.351746,504.903198,287.096527,505.011719,287.661926,504.650726C287.952362,504.452942,288.143646,504.139587,288.186829,503.790863C288.255676,503.396912,288.190155,502.991241,288.000793,502.639008C287.708282,502.119263,287.082794,501.886749,286.52179,502.089203C286.174591,502.22821,285.91684,502.527496,285.830872,502.891449L285.773743,503.157562L284.995911,503.091644C285.048401,502.241577,285.696747,501.54776,286.541321,501.437836C286.752106,501.406494,286.965851,501.400604,287.17804,501.420258z"/>
    <path transform="matrix(1,0,0,-1,0,0)" style="stroke:none;fill:#ff0000;fill-rule:evenodd" d="M288.271484,515.430603L289.085938,515.430603L289.085938,516.109314L288.271484,516.109314L288.271484,520.018005L287.666504,520.018005L284.914063,516.109314L284.914063,515.430603L287.530762,515.430603L287.530762,513.986267L288.271484,513.986267L288.271484,515.430603zM285.64209,516.109314L287.530762,518.829041L287.530762,516.109314L285.64209,516.109314z"/>
    <path transform="matrix(1,0,0,-1,0,0)" style="stroke:none;fill:#ff0000;fill-rule:evenodd" d="M261.175415,530.659302C262.184265,530.703857,262.98233,531.529419,262.992798,532.539185C263.023834,533.056763,262.77475,533.551392,262.340454,533.834595C262.19339,533.923157,262.032959,533.987305,261.865356,534.024536L262.058228,534.127075C262.644653,534.479614,262.86731,535.220581,262.572388,535.838013C262.314575,536.388367,261.779572,536.756592,261.173462,536.800903C260.765778,536.852844,260.352051,536.776917,259.98938,536.583618C259.614349,536.366333,259.3349,536.015747,259.206665,535.601685L259.116821,535.25061L259.857544,535.118774L259.911743,535.373169C259.969635,535.645813,260.131805,535.885681,260.36496,536.038452C260.598114,536.191162,260.882782,536.243958,261.155884,536.18811C261.612091,536.120972,261.946594,535.723816,261.935181,535.262817C261.954773,534.878906,261.722168,534.52655,261.36145,534.393677C261.119568,534.280334,260.851257,534.235107,260.585571,534.262817L260.50354,533.612915L260.653442,533.649048C261.160645,533.802185,261.709045,533.616699,262.019165,533.187134C262.327728,532.688782,262.256622,532.044678,261.846802,531.62561C261.409393,531.174683,260.698853,531.138062,260.217407,531.541626C259.980865,531.790833,259.828949,532.108337,259.783325,532.448853L259.043091,532.349731C259.102173,531.50238,259.740631,530.809021,260.5802,530.680298C260.777069,530.649536,260.976868,530.642456,261.175415,530.659302z"/>
    <path transform="matrix(1,0,0,-1,0,0)" style="stroke:none;fill:#00a000;fill-rule:evenodd" d="M389.079071,527.501831C389.079071,519.218567,382.327942,512.503662,374,512.503662C365.672058,512.503662,358.920929,519.218567,358.920929,527.501831C358.920929,535.785095,365.672058,542.5,374,542.5C382.327942,542.5,389.079071,535.785095,389.079071,527.501831zM388.429352,527.501831C388.429352,519.5755,381.969116,513.149902,374,513.149902C366.030884,513.149902,359.570648,519.5755,359.570648,527.501831C359.570648,535.428162,366.030884,541.85376,374,541.85376C381.969116,541.85376,388.429352,535.428162,388.429352,527.501831z"/>
</svg>
