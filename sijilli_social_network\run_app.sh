#!/bin/bash

echo "Starting Sijilli Social Network App..."
echo

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    echo "Flutter not found in PATH. Please install Flutter first."
    echo "You can download it from: https://docs.flutter.dev/get-started/install"
    exit 1
fi

echo "Flutter found. Getting dependencies..."
flutter pub get

echo
echo "Starting app on Chrome..."
flutter run -d chrome --web-port=8080
