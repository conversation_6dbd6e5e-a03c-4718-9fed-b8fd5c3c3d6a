import 'package:flutter/material.dart';
import '../models/models.dart';
import '../constants/app_theme.dart';
import '../services/pocketbase_service.dart';

class AppointmentCard extends StatelessWidget {
  final Appointment appointment;
  final VoidCallback? onTap;
  final VoidCallback? onCopy;
  final VoidCallback? onDelete;

  const AppointmentCard({
    super.key,
    required this.appointment,
    this.onTap,
    this.onCopy,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final currentUser = PocketbaseService.instance.currentUser;
    final isOwner = currentUser != null && appointment.isOwner(currentUser.id);
    final daysRemaining = _calculateDaysRemaining();

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        // إزالة الارتفاع الثابت للسماح بالتكيف مع المحتوى
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.borderColor, width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          children: [
            // Header: Owner info (left) + Days remaining (right)
            _buildHeader(daysRemaining),

            // Bottom: Copy button (left) + Details (center) + Guest (right)
            _buildBottomSection(isOwner),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(int daysRemaining) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Row(
        children: [
          // Owner info (left side)
          Row(
            children: [
              CircleAvatar(
                radius: 10, // صغر الصورة
                backgroundColor: Colors.green,
                backgroundImage: null,
                child: const Icon(
                  Icons.person,
                  size: 12,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                appointment.owner.displayName ?? appointment.owner.username,
                style: const TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ],
          ),

          const Spacer(),

          // Days remaining (right side)
          Text(
            'تبقى $daysRemaining ${daysRemaining == 1 ? 'يوم' : 'يوماً'}',
            style: const TextStyle(
              fontSize: 10,
              color: Colors.grey,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildBottomSection(bool isOwner) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: AppTheme.borderColor, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // Copy button (left)
          _buildCopyButton(),

          const SizedBox(width: 8),

          // Appointment details (center)
          Expanded(
            child: _buildAppointmentDetails(),
          ),

          const SizedBox(width: 8),

          // First guest with status indicator (right)
          if (appointment.guests.isNotEmpty)
            _buildGuestAvatar(),
        ],
      ),
    );
  }

  Widget _buildCopyButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade400),
        borderRadius: BorderRadius.circular(3),
      ),
      child: InkWell(
        onTap: onCopy,
        child: const Center(
          child: Text(
            'نسخة',
            style: TextStyle(
              fontSize: 10,
              color: Colors.black87,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppointmentDetails() {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Title (moved from center to here)
          Text(
            appointment.title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),

          // Location
          Text(
            appointment.location,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 2),

          // Date and Time in one line
          Text(
            '${_formatDate()} - ${_formatTime(appointment.time)}',
            style: const TextStyle(
              fontSize: 11,
              color: Colors.black54,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildGuestAvatar() {
    // TODO: Get actual invitation status from database
    final hasRedIndicator = true; // Placeholder for invitation status

    return Stack(
      children: [
        Container(
          width: 28,
          height: 28,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.black87,
          ),
          child: const Icon(
            Icons.person,
            color: Colors.white,
            size: 16,
          ),
        ),

        // Red status indicator (top-right)
        if (hasRedIndicator)
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              width: 10,
              height: 10,
              decoration: BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 1.5),
              ),
            ),
          ),
      ],
    );
  }

  int _calculateDaysRemaining() {
    final now = DateTime.now();
    final appointmentDate = appointment.datetime;
    final difference = appointmentDate.difference(now).inDays;
    // للتجربة، إذا كان التاريخ في المستقبل البعيد، أعطي 30 يوم
    if (appointmentDate.year > 2030) {
      return 30;
    }
    return difference > 0 ? difference : 0;
  }

  Color _getDaysRemainingColor(int days) {
    if (days <= 1) return AppTheme.errorColor;
    if (days <= 7) return AppTheme.warningColor;
    return AppTheme.successColor;
  }

  String _formatDate() {
    final date = appointment.datetime;
    final weekdays = [
      'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس',
      'الجمعة', 'السبت', 'الأحد'
    ];
    final months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    final weekday = weekdays[date.weekday - 1];
    final month = months[date.month - 1];

    return '$weekday ${date.day} $month ${date.year}';
  }

  String _formatTime(String time24) {
    try {
      // Parse the time (assuming format HH:mm)
      final parts = time24.split(':');
      if (parts.length != 2) return time24;

      final hour = int.parse(parts[0]);
      final minute = parts[1];

      if (hour == 0) {
        return '12:$minute صباحاً';
      } else if (hour < 12) {
        return '$hour:$minute صباحاً';
      } else if (hour == 12) {
        return '12:$minute مساءً';
      } else {
        final hour12 = hour - 12;
        return '$hour12:$minute مساءً';
      }
    } catch (e) {
      return time24; // Return original if parsing fails
    }
  }
}
