import 'package:flutter/material.dart';
import '../../constants/app_theme.dart';

class AppointmentDetailsScreen extends StatelessWidget {
  final String appointmentId;

  const AppointmentDetailsScreen({
    super.key,
    required this.appointmentId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الموعد'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.event_note_outlined,
              size: 64,
              color: AppTheme.textHint,
            ),
            const SizedBox(height: 16),
            Text(
              'تفاصيل الموعد',
              style: AppTheme.headlineSmall.copyWith(color: AppTheme.textSecondary),
            ),
            const SizedBox(height: 8),
            Text(
              'معرف الموعد: $appointmentId',
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.textHint),
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم إضافة تفاصيل الموعد قريباً',
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.textHint),
            ),
          ],
        ),
      ),
    );
  }
}
