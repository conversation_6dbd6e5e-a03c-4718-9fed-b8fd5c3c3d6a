<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سِجِلّي - اختبار بسيط</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }

        .logo {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 16px;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            text-align: right;
        }

        .form-input:focus {
            outline: none;
            background: white;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .btn-primary {
            background: #28a745;
            color: white;
        }

        .btn-primary:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .error {
            background: #dc3545;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .success {
            background: #28a745;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">سِجِلّي</div>
        <div class="subtitle">اختبار تسجيل الدخول البسيط</div>
        
        <div id="errorMessage" class="error"></div>
        <div id="successMessage" class="success"></div>
        
        <div class="form-group">
            <input type="email" class="form-input" placeholder="البريد الإلكتروني" 
                   id="emailInput" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <input type="password" class="form-input" placeholder="كلمة المرور" 
                   id="passwordInput" value="123456789">
        </div>
        
        <button class="btn btn-primary" onclick="testLogin()" id="loginBtn">
            اختبار تسجيل الدخول
        </button>
        
        <button class="btn btn-secondary" onclick="testConnection()" id="testBtn">
            اختبار الاتصال مع Pocketbase
        </button>
        
        <div id="status" class="status">
            جاري التحميل...
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/pocketbase@0.21.3/dist/pocketbase.umd.js"></script>
    <script>
        console.log('Script started');
        
        // Global variables
        let pb = null;
        const POCKETBASE_URL = 'http://127.0.0.1:8090';
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            initializePocketbase();
        });
        
        function initializePocketbase() {
            try {
                console.log('Initializing PocketBase...');
                pb = new PocketBase(POCKETBASE_URL);
                console.log('PocketBase initialized:', pb);
                showStatus('PocketBase تم تهيئته بنجاح', 'success');
                testConnection();
            } catch (error) {
                console.error('Failed to initialize PocketBase:', error);
                showStatus('خطأ في تهيئة PocketBase: ' + error.message, 'error');
            }
        }
        
        async function testConnection() {
            console.log('Testing connection...');
            showStatus('جاري اختبار الاتصال...', 'info');
            
            try {
                await pb.health.check();
                console.log('Connection successful');
                showStatus('✅ الاتصال مع PocketBase ناجح', 'success');
            } catch (error) {
                console.error('Connection failed:', error);
                showStatus('❌ فشل الاتصال: ' + error.message, 'error');
            }
        }
        
        async function testLogin() {
            console.log('Testing login...');
            
            const email = document.getElementById('emailInput').value;
            const password = document.getElementById('passwordInput').value;
            const loginBtn = document.getElementById('loginBtn');
            
            if (!email || !password) {
                showError('يرجى إدخال البريد الإلكتروني وكلمة المرور');
                return;
            }
            
            if (!pb) {
                showError('PocketBase غير مهيأ');
                return;
            }
            
            loginBtn.disabled = true;
            loginBtn.textContent = 'جاري تسجيل الدخول...';
            showStatus('جاري محاولة تسجيل الدخول...', 'info');
            
            try {
                console.log('Attempting login with:', email);
                const authData = await pb.collection('users').authWithPassword(email, password);
                console.log('Login successful:', authData);
                
                showSuccess('تم تسجيل الدخول بنجاح!');
                showStatus('✅ تم تسجيل الدخول كـ: ' + authData.record.username, 'success');
                
            } catch (error) {
                console.error('Login failed:', error);
                showError('فشل تسجيل الدخول: ' + error.message);
                showStatus('❌ فشل تسجيل الدخول', 'error');
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = 'اختبار تسجيل الدخول';
            }
        }
        
        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            setTimeout(() => {
                errorEl.style.display = 'none';
            }, 5000);
        }
        
        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.style.display = 'block';
            setTimeout(() => {
                successEl.style.display = 'none';
            }, 5000);
        }
        
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.style.background = type === 'success' ? '#28a745' : 
                                      type === 'error' ? '#dc3545' : 
                                      'rgba(255, 255, 255, 0.1)';
        }
        
        console.log('Script loaded successfully');
    </script>
</body>
</html>
