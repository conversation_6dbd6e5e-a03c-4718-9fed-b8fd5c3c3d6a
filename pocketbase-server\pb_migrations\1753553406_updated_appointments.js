/// <reference path="../pb_data/types.d.ts" />
migrate((app) => {
  const collection = app.findCollectionByNameOrId("pbc_1037645436")

  // add field
  collection.fields.addAt(11, new Field({
    "hidden": false,
    "id": "bool3054334843",
    "name": "hasTime",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "bool"
  }))

  // add field
  collection.fields.addAt(12, new Field({
    "autogeneratePattern": "",
    "hidden": false,
    "id": "text1872009285",
    "max": 0,
    "min": 0,
    "name": "time",
    "pattern": "",
    "presentable": false,
    "primaryKey": false,
    "required": false,
    "system": false,
    "type": "text"
  }))

  // add field
  collection.fields.addAt(13, new Field({
    "hidden": false,
    "id": "select2682684254",
    "maxSelect": 1,
    "name": "calendarType",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "select",
    "values": [
      "gregorian, hijri"
    ]
  }))

  return app.save(collection)
}, (app) => {
  const collection = app.findCollectionByNameOrId("pbc_1037645436")

  // remove field
  collection.fields.removeById("bool3054334843")

  // remove field
  collection.fields.removeById("text1872009285")

  // remove field
  collection.fields.removeById("select2682684254")

  return app.save(collection)
})
