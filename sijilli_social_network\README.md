# سجلي (Sijilli) - شبكة اجتماعية لإدارة المواعيد

تطبيق اجتماعي يركز على تنظيم المواعيد وإدارة الدعوات بين الأفراد، مبني باستخدام Flutter و Pocketbase.

## 🌟 الميزات الرئيسية

- **إدارة المواعيد**: إنشاء وتنظيم المواعيد مع دعم الخصوصية (عام/خاص)
- **نظام الدعوات**: دعوة المستخدمين مع تتبع حالات الاستجابة
- **البحث الاجتماعي**: البحث عن المستخدمين ومتابعتهم
- **الإشعارات**: تلقي إشعارات للدعوات والتحديثات
- **دعم RTL/LTR**: دعم كامل للغتين العربية والإنجليزية

## 🏗️ البنية التقنية

### Frontend (Flutter)
- **Framework**: Flutter 3.32.4
- **State Management**: Provider
- **Navigation**: go_router
- **UI**: Material Design 3
- **Localization**: flutter_localizations

### Backend (Pocketbase)
- **Database**: SQLite مع Pocketbase
- **Authentication**: نظام مصادقة مدمج
- **Real-time**: WebSocket للتحديثات المباشرة
- **File Storage**: تخزين الصور والملفات

## 📱 الشاشات الرئيسية

1. **الرئيسية** - عرض المواعيد الشخصية
2. **البحث** - البحث عن المستخدمين
3. **إضافة موعد** - إنشاء مواعيد جديدة
4. **الإشعارات** - عرض الإشعارات والدعوات
5. **الإعدادات** - إدارة الحساب والتطبيق

## 🗄️ نماذج البيانات

### User (المستخدم)
- معرف فريد، اسم المستخدم، البريد الإلكتروني
- الدور (user, approved, admin)
- بيانات الملف الشخصي

### Appointment (الموعد)
- العنوان، الخصوصية، المنطقة، المبنى
- التاريخ والوقت
- المالك والضيوف (حد أقصى 10)

### Invitation (الدعوة)
- حالات: مدعو، قبل، رفض، قبل ثم حذف
- تتبع أوقات الاستجابة

### Notification (الإشعار)
- أنواع مختلفة من الإشعارات
- حالة القراءة

### Follow (المتابعة)
- نظام المتابعة بين المستخدمين
- تصنيف العلاقات (أتابعه، يتابعني، صديق)

## 🚀 البدء السريع

### التشغيل المباشر (بدون Pocketbase)
للتجربة السريعة مع بيانات تجريبية:

**Windows:**
```bash
run_app.bat
```

**Linux/Mac:**
```bash
chmod +x run_app.sh
./run_app.sh
```

أو يدوياً:
```bash
flutter pub get
flutter run -d chrome --web-port=8080
```

### 🎨 عرض تجريبي لبطاقة الموعد
بعد تشغيل التطبيق:
1. اذهب إلى تبويب "الإعدادات"
2. اضغط على "عرض تجريبي - بطاقة الموعد"
3. شاهد التصميم المطابق للـ PRD

### المتطلبات
- Flutter SDK 3.8.1+
- Dart 3.0+
- متصفح ويب (Chrome مفضل)
- Pocketbase Server (للإنتاج)

### التثبيت الكامل

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd sijilli_social_network
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **إعداد Pocketbase (اختياري)**
- تحميل Pocketbase من الموقع الرسمي
- استيراد schema من `database/schema_backup.json`
- تشغيل الخادم على المنفذ 8090

4. **تشغيل التطبيق**
```bash
flutter run -d chrome  # للويب
flutter run -d android # للأندرويد
flutter run -d ios     # للآيفون
```

## 📁 هيكل المشروع

```
lib/
├── constants/          # الثوابت والألوان
├── models/            # نماذج البيانات
├── services/          # خدمات API و Pocketbase
├── providers/         # مزودي الحالة
├── screens/           # شاشات التطبيق
│   ├── auth/         # شاشات المصادقة
│   ├── home/         # الشاشة الرئيسية
│   ├── search/       # شاشة البحث
│   ├── appointment/  # شاشات المواعيد
│   ├── notifications/ # شاشة الإشعارات
│   ├── settings/     # شاشة الإعدادات
│   └── profile/      # الملف الشخصي
├── widgets/           # مكونات قابلة للإعادة
├── utils/            # أدوات مساعدة
└── main.dart         # نقطة البداية
```

## 🔧 التكوين

### Pocketbase URL
يمكن تغيير رابط خادم Pocketbase في:
```dart
// lib/constants/app_constants.dart
static const String defaultPocketbaseUrl = 'http://127.0.0.1:8090';
```

### الألوان والثيم
تخصيص الألوان في:
```dart
// lib/constants/app_theme.dart
```

## ✅ المنجز حديثاً

### 🎨 بطاقة الموعد (Appointment Card)
- ✅ تصميم مطابق 100% للـ PRD المطلوب
- ✅ نسبة 4:1 كما هو محدد
- ✅ عرض صورة المالك واسمه
- ✅ عداد الأيام المتبقية مع ألوان تدريجية
- ✅ العنوان في الوسط بخط واضح
- ✅ تفاصيل المكان والتاريخ والوقت
- ✅ زر النسخ للمالك وغير المالك
- ✅ صورة المدعو الأول مع مؤشر الحالة
- ✅ دعم RTL للنصوص العربية
- ✅ ألوان متناسقة مع ثيم التطبيق

### 📱 صفحة عرض تجريبية
- ✅ صفحة مخصصة لعرض تصميم البطاقة
- ✅ أمثلة متنوعة من المواعيد
- ✅ قائمة بالميزات المطبقة
- ✅ سهولة الوصول من الإعدادات

## 📝 المهام المستقبلية

- [ ] إكمال نظام الإشعارات
- [ ] إضافة نظام المتابعة
- [ ] تطوير الملف الشخصي
- [ ] إضافة تفاصيل المواعيد
- [ ] دعم الصور والملفات
- [ ] تحسين الأداء
- [ ] إضافة الاختبارات
- [ ] دعم الوضع المظلم

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 التواصل

للاستفسارات والدعم، يرجى التواصل عبر GitHub Issues.
