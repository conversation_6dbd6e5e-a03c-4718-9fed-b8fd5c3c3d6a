import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../services/pocketbase_service.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final currentUser = PocketbaseService.instance.currentUser;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
      ),
      body: ListView(
        children: [
          // User Info Section
          if (currentUser != null) ...[
            Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 30,
                      backgroundColor: AppTheme.primaryColor,
                      child: Text(
                        currentUser.username[0].toUpperCase(),
                        style: AppTheme.headlineSmall.copyWith(color: Colors.white),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            currentUser.displayName ?? currentUser.username,
                            style: AppTheme.headlineSmall,
                          ),
                          Text(
                            '@${currentUser.username}',
                            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
                          ),
                          Text(
                            currentUser.email,
                            style: AppTheme.bodySmall.copyWith(color: AppTheme.textHint),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () => _editProfile(context),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],

          // Settings Sections
          _buildSettingsSection(
            context,
            title: 'الحساب',
            items: [
              _SettingsItem(
                icon: Icons.person_outline,
                title: 'تعديل الملف الشخصي',
                onTap: () => _editProfile(context),
              ),
              _SettingsItem(
                icon: Icons.link,
                title: 'الرابط الشخصي',
                subtitle: 'sijilli.com/${currentUser?.username ?? ''}',
                onTap: () => _copyProfileLink(context),
              ),
              _SettingsItem(
                icon: Icons.archive_outlined,
                title: 'الأرشيف',
                onTap: () => _openArchive(context),
              ),
            ],
          ),

          _buildSettingsSection(
            context,
            title: 'التطبيق',
            items: [
              _SettingsItem(
                icon: Icons.language,
                title: 'اللغة',
                subtitle: 'العربية',
                onTap: () => _changeLanguage(context),
              ),
              _SettingsItem(
                icon: Icons.dark_mode_outlined,
                title: 'المظهر',
                subtitle: 'فاتح',
                onTap: () => _changeTheme(context),
              ),
              _SettingsItem(
                icon: Icons.notifications_outlined,
                title: 'الإشعارات',
                onTap: () => _notificationSettings(context),
              ),
            ],
          ),

          _buildSettingsSection(
            context,
            title: 'المساعدة',
            items: [
              _SettingsItem(
                icon: Icons.preview,
                title: 'عرض تجريبي - بطاقة الموعد',
                subtitle: 'مشاهدة تصميم البطاقة',
                onTap: () => context.go('/demo'),
              ),
              _SettingsItem(
                icon: Icons.help_outline,
                title: 'المساعدة والدعم',
                onTap: () => _openHelp(context),
              ),
              _SettingsItem(
                icon: Icons.info_outline,
                title: 'حول التطبيق',
                subtitle: 'الإصدار ${AppConstants.appVersion}',
                onTap: () => _showAbout(context),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Logout Button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ElevatedButton(
              onPressed: () => _logout(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.errorColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('تسجيل الخروج'),
            ),
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(
    BuildContext context, {
    required String title,
    required List<_SettingsItem> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: AppTheme.labelLarge.copyWith(color: AppTheme.primaryColor),
          ),
        ),
        Card(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: items.map((item) {
              final isLast = item == items.last;
              return Column(
                children: [
                  ListTile(
                    leading: Icon(item.icon),
                    title: Text(item.title),
                    subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: item.onTap,
                  ),
                  if (!isLast) const Divider(height: 1),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  void _editProfile(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة تعديل الملف الشخصي قريباً')),
    );
  }

  void _copyProfileLink(BuildContext context) {
    final currentUser = PocketbaseService.instance.currentUser;
    if (currentUser != null) {
      // TODO: Copy to clipboard
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم نسخ الرابط: sijilli.com/${currentUser.username}')),
      );
    }
  }

  void _openArchive(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة الأرشيف قريباً')),
    );
  }

  void _changeLanguage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة تغيير اللغة قريباً')),
    );
  }

  void _changeTheme(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة تغيير المظهر قريباً')),
    );
  }

  void _notificationSettings(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة إعدادات الإشعارات قريباً')),
    );
  }

  void _openHelp(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة المساعدة قريباً')),
    );
  }

  void _showAbout(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      applicationLegalese: '© 2025 ${AppConstants.appName}',
      children: [
        const Text('تطبيق لإدارة المواعيد والدعوات الاجتماعية'),
      ],
    );
  }

  Future<void> _logout(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.errorColor),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      PocketbaseService.instance.signOut();
      if (context.mounted) {
        context.go(AppConstants.loginRoute);
      }
    }
  }
}

class _SettingsItem {
  final IconData icon;
  final String title;
  final String? subtitle;
  final VoidCallback onTap;

  _SettingsItem({
    required this.icon,
    required this.title,
    this.subtitle,
    required this.onTap,
  });
}
