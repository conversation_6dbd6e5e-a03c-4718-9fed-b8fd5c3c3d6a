<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sijilli - تطبيق الشبكة الاجتماعية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            direction: rtl;
        }

        .app-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .app-bar {
            background: #2196F3;
            color: white;
            padding: 16px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
        }

        .appointment-card {
            margin: 16px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .card-header {
            padding: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .owner-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4CAF50;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .owner-name {
            font-size: 11px;
            font-weight: 500;
            color: #333;
        }

        .days-remaining {
            font-size: 10px;
            color: #666;
        }



        .card-bottom {
            padding: 12px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .copy-btn {
            padding: 6px 12px;
            border: 1px solid #ccc;
            border-radius: 3px;
            background: white;
            font-size: 10px;
            cursor: pointer;
        }

        .appointment-details {
            text-align: center;
            flex: 1;
            margin: 0 8px;
            direction: rtl;
        }

        .appointment-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .location {
            font-size: 12px;
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }

        .datetime {
            font-size: 11px;
            color: #666;
        }

        .guest-avatar {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            position: relative;
        }

        .status-indicator {
            position: absolute;
            top: 0;
            right: 0;
            width: 10px;
            height: 10px;
            background: red;
            border-radius: 50%;
            border: 1.5px solid white;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 400px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-around;
            padding: 8px 0;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: #666;
            text-decoration: none;
            font-size: 12px;
        }

        .nav-item.active {
            color: #2196F3;
        }

        .nav-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .demo-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 12px;
            margin: 16px;
            border-radius: 4px;
            text-align: center;
            font-size: 14px;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="app-bar">
            مواعيدي
        </div>

        <div class="demo-note">
            🎉 هذا عرض تجريبي لتطبيق Sijilli<br>
            التطبيق الكامل مبني بـ Flutter ومتصل بـ Pocketbase
        </div>

        <!-- Appointment Card 1 -->
        <div class="appointment-card">
            <div class="card-header">
                <div class="owner-info">
                    <div class="avatar">👤</div>
                    <div class="owner-name">السيد مدير المدرسة</div>
                </div>
                <div class="days-remaining">تبقى 30 يوماً</div>
            </div>
            <div class="card-bottom">
                <button class="copy-btn">نسخة</button>
                <div class="appointment-details">
                    <div class="appointment-title">اجتماع الكادر التعليمي السنوي</div>
                    <div class="location">النعيم - مدرسة حمدان</div>
                    <div class="datetime">السبت 22 يونيو 2036 - 9:00 مساءً</div>
                </div>
                <div class="guest-avatar">
                    👤
                    <div class="status-indicator"></div>
                </div>
            </div>
        </div>

        <!-- Appointment Card 2 -->
        <div class="appointment-card">
            <div class="card-header">
                <div class="owner-info">
                    <div class="avatar">👤</div>
                    <div class="owner-name">السيد مدير المدرسة</div>
                </div>
                <div class="days-remaining">تبقى 7 أيام</div>
            </div>
            <div class="card-bottom">
                <button class="copy-btn">نسخة</button>
                <div class="appointment-details">
                    <div class="appointment-title">ورشة تطوير المناهج</div>
                    <div class="location">الفروانية - مركز التدريب</div>
                    <div class="datetime">الأحد 21 يوليو 2024 - 2:00 مساءً</div>
                </div>
                <div class="guest-avatar">
                    👤
                    <div class="status-indicator"></div>
                </div>
            </div>
        </div>

        <!-- Appointment Card 3 -->
        <div class="appointment-card">
            <div class="card-header">
                <div class="owner-info">
                    <div class="avatar">👤</div>
                    <div class="owner-name">السيد مدير المدرسة</div>
                </div>
                <div class="days-remaining">تبقى 2 يوم</div>
            </div>
            <div class="card-bottom">
                <button class="copy-btn">نسخة</button>
                <div class="appointment-details">
                    <div class="appointment-title">اجتماع أولياء الأمور</div>
                    <div class="location">حولي</div>
                    <div class="datetime">الثلاثاء 16 يوليو 2024 - 6:30 مساءً</div>
                </div>
                <div class="guest-avatar" style="background: #ccc;">
                    
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <a href="#" class="nav-item active">
                <div class="nav-icon">🏠</div>
                <div>الرئيسية</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">🔍</div>
                <div>البحث</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">➕</div>
                <div>إضافة</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">🔔</div>
                <div>الإشعارات</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">⚙️</div>
                <div>الإعدادات</div>
            </a>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.querySelectorAll('.copy-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('تم نسخ بيانات الموعد');
            });
        });

        document.querySelectorAll('.appointment-card').forEach(card => {
            card.addEventListener('click', function() {
                alert('عرض تفاصيل الموعد');
            });
        });

        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
                alert('تم النقر على: ' + this.querySelector('div:last-child').textContent);
            });
        });
    </script>
</body>
</html>
