@echo off
echo Starting Sijilli Social Network App...
echo.

REM Check if Flutter is available
where flutter >nul 2>nul
if %errorlevel% neq 0 (
    echo Flutter not found in PATH. Please install Flutter first.
    echo You can download it from: https://docs.flutter.dev/get-started/install
    pause
    exit /b 1
)

echo Flutter found. Getting dependencies...
call flutter pub get

echo.
echo Starting app on Chrome...
call flutter run -d chrome --web-port=8080

pause
