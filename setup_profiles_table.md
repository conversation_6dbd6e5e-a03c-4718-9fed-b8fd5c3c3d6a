# إعداد جدول البروفايل في Pocketbase

## الخطوات المطلوبة:

### 1. فتح واجهة إدارة Pocketbase
- اذهب إلى: http://127.0.0.1:8090/_/
- سجل الدخول بحساب الإدارة

### 2. إنشاء Collection جديد
1. اضغط على "Collections" في القائمة الجانبية
2. اضغط على "New collection"
3. اختر "Base collection"
4. اسم الـ Collection: `profiles`

### 3. إضافة الحقول (Fields):

#### حقل المستخدم (user):
- **Name:** `user`
- **Type:** `Relation`
- **Required:** ✅ Yes
- **Unique:** ✅ Yes
- **Collection:** `users`
- **Cascade delete:** ✅ Yes
- **Min select:** 1
- **Max select:** 1

#### حقل الاسم المعروض (display_name):
- **Name:** `display_name`
- **Type:** `Text`
- **Required:** ❌ No
- **Max length:** 100

#### حقل النبذة الشخصية (bio):
- **Name:** `bio`
- **Type:** `Text`
- **Required:** ❌ No
- **Max length:** 500

#### حقل الصورة الشخصية (avatar):
- **Name:** `avatar`
- **Type:** `File`
- **Required:** ❌ No
- **Max select:** 1
- **Max size:** 5MB
- **Mime types:** image/jpeg, image/png, image/gif, image/webp

#### حقل رقم الهاتف (phone):
- **Name:** `phone`
- **Type:** `Text`
- **Required:** ❌ No
- **Max length:** 20
- **Pattern:** `^[+]?[0-9\-\s()]*$`

#### حقل الموقع (location):
- **Name:** `location`
- **Type:** `Text`
- **Required:** ❌ No
- **Max length:** 100

#### حقل تاريخ الميلاد (birth_date):
- **Name:** `birth_date`
- **Type:** `Date`
- **Required:** ❌ No

#### حقل الجنس (gender):
- **Name:** `gender`
- **Type:** `Select`
- **Required:** ❌ No
- **Max select:** 1
- **Values:** `male`, `female`, `other`, `prefer_not_to_say`

#### حقل الموقع الشخصي (website):
- **Name:** `website`
- **Type:** `URL`
- **Required:** ❌ No

#### حقل روابط التواصل (social_links):
- **Name:** `social_links`
- **Type:** `JSON`
- **Required:** ❌ No

#### حقل إعدادات الخصوصية (privacy_settings):
- **Name:** `privacy_settings`
- **Type:** `JSON`
- **Required:** ❌ No

#### حقل إعدادات الإشعارات (notification_settings):
- **Name:** `notification_settings`
- **Type:** `JSON`
- **Required:** ❌ No

#### حقل التحقق (is_verified):
- **Name:** `is_verified`
- **Type:** `Bool`
- **Required:** ❌ No

#### حقل العمومية (is_public):
- **Name:** `is_public`
- **Type:** `Bool`
- **Required:** ❌ No

### 4. إعداد قواعد الوصول (API Rules):

#### List rule:
```
@request.auth.id != "" && (is_public = true || user.id = @request.auth.id)
```

#### View rule:
```
@request.auth.id != "" && (is_public = true || user.id = @request.auth.id)
```

#### Create rule:
```
@request.auth.id != "" && user.id = @request.auth.id
```

#### Update rule:
```
@request.auth.id != "" && user.id = @request.auth.id
```

#### Delete rule:
```
@request.auth.id != "" && user.id = @request.auth.id
```

### 5. حفظ الـ Collection
- اضغط على "Save" لحفظ الجدول

### 6. اختبار التطبيق
- ارجع للتطبيق وجرب تعديل البروفايل
- يجب أن يعمل حفظ الاسم المعروض الآن

## ملاحظات مهمة:
- تأكد من أن خادم Pocketbase يعمل
- تأكد من أن جدول `users` موجود
- الجدول سيُنشأ تلقائياً عند أول استخدام للتطبيق
- يمكن إضافة المزيد من الحقول لاحقاً حسب الحاجة
