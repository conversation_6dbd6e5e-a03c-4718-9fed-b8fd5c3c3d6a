import 'package:flutter/material.dart';
import '../../constants/app_theme.dart';

class ProfileScreen extends StatelessWidget {
  final String username;

  const ProfileScreen({
    super.key,
    required this.username,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('@$username'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.person_outline,
              size: 64,
              color: AppTheme.textHint,
            ),
            const SizedBox(height: 16),
            Text(
              'الملف الشخصي',
              style: AppTheme.headlineSmall.copyWith(color: AppTheme.textSecondary),
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم إضافة الملف الشخصي قريباً',
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.textHint),
            ),
          ],
        ),
      ),
    );
  }
}
