/// <reference path="../pb_data/types.d.ts" />
migrate((app) => {
  const collection = app.findCollectionByNameOrId("pbc_1037645436")

  // remove field
  collection.fields.removeById("select2682684254")

  return app.save(collection)
}, (app) => {
  const collection = app.findCollectionByNameOrId("pbc_1037645436")

  // add field
  collection.fields.addAt(5, new Field({
    "hidden": false,
    "id": "select2682684254",
    "maxSelect": 1,
    "name": "calendar_type",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "select",
    "values": [
      "gregorian",
      "hijri"
    ]
  }))

  return app.save(collection)
})
