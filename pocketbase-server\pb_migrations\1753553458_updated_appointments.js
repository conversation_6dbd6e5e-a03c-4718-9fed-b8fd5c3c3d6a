/// <reference path="../pb_data/types.d.ts" />
migrate((app) => {
  const collection = app.findCollectionByNameOrId("pbc_1037645436")

  // update field
  collection.fields.addAt(13, new Field({
    "hidden": false,
    "id": "select2682684254",
    "maxSelect": 1,
    "name": "calendarType",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "select",
    "values": [
      "gregorian",
      "hijri"
    ]
  }))

  return app.save(collection)
}, (app) => {
  const collection = app.findCollectionByNameOrId("pbc_1037645436")

  // update field
  collection.fields.addAt(13, new Field({
    "hidden": false,
    "id": "select2682684254",
    "maxSelect": 1,
    "name": "calendarType",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "select",
    "values": [
      "gregorian, hijri"
    ]
  }))

  return app.save(collection)
})
