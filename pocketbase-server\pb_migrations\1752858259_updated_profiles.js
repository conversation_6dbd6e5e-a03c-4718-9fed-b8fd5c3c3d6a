/// <reference path="../pb_data/types.d.ts" />
migrate((app) => {
  const collection = app.findCollectionByNameOrId("pbc_3414089001")

  // update collection data
  unmarshal({
    "createRule": "@request.auth.id != \"\" && user.id = @request.auth.id",
    "deleteRule": "@request.auth.id != \"\" && user.id = @request.auth.id",
    "listRule": "@request.auth.id != \"\" && (is_public = true || user.id = @request.auth.id)",
    "updateRule": "@request.auth.id != \"\" && user.id = @request.auth.id",
    "viewRule": "@request.auth.id != \"\" && (is_public = true || user.id = @request.auth.id)"
  }, collection)

  // add field
  collection.fields.addAt(2, new Field({
    "autogeneratePattern": "",
    "hidden": false,
    "id": "text3578368839",
    "max": 100,
    "min": 1,
    "name": "display_name",
    "pattern": "",
    "presentable": false,
    "primaryKey": false,
    "required": false,
    "system": false,
    "type": "text"
  }))

  // add field
  collection.fields.addAt(3, new Field({
    "autogeneratePattern": "",
    "hidden": false,
    "id": "text3709889147",
    "max": 500,
    "min": 0,
    "name": "bio",
    "pattern": "",
    "presentable": false,
    "primaryKey": false,
    "required": false,
    "system": false,
    "type": "text"
  }))

  // add field
  collection.fields.addAt(4, new Field({
    "hidden": false,
    "id": "bool2599192861",
    "name": "is_verified",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "bool"
  }))

  // add field
  collection.fields.addAt(5, new Field({
    "hidden": false,
    "id": "bool1811784642",
    "name": "is_public",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "bool"
  }))

  return app.save(collection)
}, (app) => {
  const collection = app.findCollectionByNameOrId("pbc_3414089001")

  // update collection data
  unmarshal({
    "createRule": null,
    "deleteRule": null,
    "listRule": null,
    "updateRule": null,
    "viewRule": null
  }, collection)

  // remove field
  collection.fields.removeById("text3578368839")

  // remove field
  collection.fields.removeById("text3709889147")

  // remove field
  collection.fields.removeById("bool2599192861")

  // remove field
  collection.fields.removeById("bool1811784642")

  return app.save(collection)
})
