class AppConstants {
  // App Info
  static const String appName = 'سجلي';
  static const String appNameEnglish = 'Sijilli';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String defaultPocketbaseUrl = 'http://127.0.0.1:8090';
  
  // Collections
  static const String usersCollection = 'users';
  static const String appointmentsCollection = 'appointments';
  static const String invitationsCollection = 'invitations';
  static const String notificationsCollection = 'notifications';
  static const String followsCollection = 'follows';
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Validation
  static const int minUsernameLength = 4;
  static const int maxUsernameLength = 24;
  static const int minPasswordLength = 8;
  static const int maxGuestsPerAppointment = 10;
  
  // Time formats
  static const String timeFormat = 'HH:mm';
  static const String dateFormat = 'yyyy-MM-dd';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';
  
  // Routes
  static const String homeRoute = '/';
  static const String loginRoute = '/login';
  static const String registerRoute = '/register';
  static const String profileRoute = '/profile';
  static const String searchRoute = '/search';
  static const String addAppointmentRoute = '/add-appointment';
  static const String notificationsRoute = '/notifications';
  static const String settingsRoute = '/settings';
  static const String appointmentDetailsRoute = '/appointment-details';
  
  // Storage Keys
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  
  // Default Values
  static const String defaultAvatarUrl = 'https://via.placeholder.com/150';
  static const String defaultRegion = 'الكويت';
  
  // Error Messages
  static const String networkErrorMessage = 'خطأ في الاتصال بالشبكة';
  static const String unknownErrorMessage = 'حدث خطأ غير متوقع';
  static const String invalidCredentialsMessage = 'بيانات الدخول غير صحيحة';
  static const String userNotFoundMessage = 'المستخدم غير موجود';
  static const String emailAlreadyExistsMessage = 'البريد الإلكتروني مستخدم بالفعل';
  static const String usernameAlreadyExistsMessage = 'اسم المستخدم مستخدم بالفعل';
  
  // Success Messages
  static const String appointmentCreatedMessage = 'تم إنشاء الموعد بنجاح';
  static const String appointmentDeletedMessage = 'تم حذف الموعد بنجاح';
  static const String invitationSentMessage = 'تم إرسال الدعوة بنجاح';
  static const String invitationAcceptedMessage = 'تم قبول الدعوة';
  static const String invitationRejectedMessage = 'تم رفض الدعوة';
  
  // Privacy Options
  static const List<String> privacyOptions = ['public', 'private'];
  static const Map<String, String> privacyLabels = {
    'public': 'عام',
    'private': 'خاص',
  };
  
  // User Roles
  static const List<String> userRoles = ['user', 'approved', 'admin'];
  static const Map<String, String> roleLabels = {
    'user': 'مستخدم',
    'approved': 'معتمد',
    'admin': 'مشرف',
  };
  
  // Invitation Status
  static const List<String> invitationStatuses = ['pending', 'accepted', 'rejected', 'deleted'];
  static const Map<String, String> invitationStatusLabels = {
    'pending': 'مدعو',
    'accepted': 'قبل',
    'rejected': 'رفض',
    'deleted': 'قبل ثم حذف',
  };
  
  // Notification Types
  static const List<String> notificationTypes = [
    'invitation',
    'invitation_accepted',
    'invitation_rejected',
    'invitation_deleted',
    'appointment_reminder',
    'follow',
    'general'
  ];
  
  // Regions (can be expanded)
  static const List<String> regions = [
    'الكويت',
    'حولي',
    'الفروانية',
    'مبارك الكبير',
    'الأحمدي',
    'الجهراء',
  ];
  
  // Time slots (can be customized)
  static const List<String> timeSlots = [
    '08:00',
    '09:00',
    '10:00',
    '11:00',
    '12:00',
    '13:00',
    '14:00',
    '15:00',
    '16:00',
    '17:00',
    '18:00',
    '19:00',
    '20:00',
    '21:00',
    '22:00',
  ];
}
