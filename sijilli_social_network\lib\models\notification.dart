import 'user.dart';
import 'appointment.dart';
import 'invitation.dart';

class AppNotification {
  final String id;
  final String userId;
  final NotificationType type;
  final String title;
  final String message;
  final Map<String, dynamic> data;
  final bool isRead;
  final DateTime created;
  final DateTime updated;

  // Expanded relations
  User? relatedUser;
  Appointment? relatedAppointment;
  Invitation? relatedInvitation;

  AppNotification({
    required this.id,
    required this.userId,
    required this.type,
    required this.title,
    required this.message,
    required this.data,
    required this.isRead,
    required this.created,
    required this.updated,
    this.relatedUser,
    this.relatedAppointment,
    this.relatedInvitation,
  });

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      type: NotificationType.fromString(json['type'] ?? 'general'),
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      isRead: json['isRead'] ?? false,
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
      relatedUser: json['expand']?['relatedUser'] != null
          ? User.fromJson(json['expand']['relatedUser'])
          : null,
      relatedAppointment: json['expand']?['relatedAppointment'] != null
          ? Appointment.fromJson(json['expand']['relatedAppointment'])
          : null,
      relatedInvitation: json['expand']?['relatedInvitation'] != null
          ? Invitation.fromJson(json['expand']['relatedInvitation'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type.toString(),
      'title': title,
      'message': message,
      'data': data,
      'isRead': isRead,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  AppNotification copyWith({
    String? id,
    String? userId,
    NotificationType? type,
    String? title,
    String? message,
    Map<String, dynamic>? data,
    bool? isRead,
    DateTime? created,
    DateTime? updated,
    User? relatedUser,
    Appointment? relatedAppointment,
    Invitation? relatedInvitation,
  }) {
    return AppNotification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      relatedUser: relatedUser ?? this.relatedUser,
      relatedAppointment: relatedAppointment ?? this.relatedAppointment,
      relatedInvitation: relatedInvitation ?? this.relatedInvitation,
    );
  }

  // Helper methods
  AppNotification markAsRead() {
    return copyWith(isRead: true);
  }

  @override
  String toString() {
    return 'AppNotification(id: $id, type: $type, title: $title, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppNotification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum NotificationType {
  invitation,        // دعوة جديدة
  invitationAccepted, // قبول دعوة
  invitationRejected, // رفض دعوة
  invitationDeleted,  // حذف موعد بعد القبول
  appointmentReminder, // تذكير بموعد
  follow,            // متابعة جديدة
  general;           // إشعار عام

  static NotificationType fromString(String type) {
    switch (type.toLowerCase()) {
      case 'invitation':
        return NotificationType.invitation;
      case 'invitation_accepted':
        return NotificationType.invitationAccepted;
      case 'invitation_rejected':
        return NotificationType.invitationRejected;
      case 'invitation_deleted':
        return NotificationType.invitationDeleted;
      case 'appointment_reminder':
        return NotificationType.appointmentReminder;
      case 'follow':
        return NotificationType.follow;
      case 'general':
        return NotificationType.general;
      default:
        return NotificationType.general;
    }
  }

  @override
  String toString() {
    return name;
  }

  String get displayName {
    switch (this) {
      case NotificationType.invitation:
        return 'دعوة جديدة';
      case NotificationType.invitationAccepted:
        return 'قبول دعوة';
      case NotificationType.invitationRejected:
        return 'رفض دعوة';
      case NotificationType.invitationDeleted:
        return 'حذف موعد';
      case NotificationType.appointmentReminder:
        return 'تذكير موعد';
      case NotificationType.follow:
        return 'متابعة جديدة';
      case NotificationType.general:
        return 'إشعار عام';
    }
  }
}
