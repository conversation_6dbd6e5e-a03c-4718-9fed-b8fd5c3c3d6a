import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';
import '../screens/main/main_screen.dart';
import '../screens/home/<USER>';
import '../screens/search/search_screen.dart';
import '../screens/appointment/add_appointment_screen.dart';
import '../screens/notifications/notifications_screen.dart';
import '../screens/settings/settings_screen.dart';
import '../screens/profile/profile_screen.dart';
import '../screens/appointment/appointment_details_screen.dart';
import '../screens/demo/demo_screen.dart';
import '../services/pocketbase_service.dart';
import '../constants/app_constants.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: AppConstants.homeRoute,
    redirect: (context, state) {
      final isAuthenticated = PocketbaseService.instance.isAuthenticated;
      final isAuthRoute = state.matchedLocation == AppConstants.loginRoute || 
                         state.matchedLocation == AppConstants.registerRoute;
      
      // If not authenticated and not on auth route, redirect to login
      if (!isAuthenticated && !isAuthRoute) {
        return AppConstants.loginRoute;
      }
      
      // If authenticated and on auth route, redirect to home
      if (isAuthenticated && isAuthRoute) {
        return AppConstants.homeRoute;
      }
      
      return null;
    },
    routes: [
      // Auth Routes
      GoRoute(
        path: AppConstants.loginRoute,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppConstants.registerRoute,
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      
      // Main App Routes with Bottom Navigation
      ShellRoute(
        builder: (context, state, child) => MainScreen(child: child),
        routes: [
          // Home Tab
          GoRoute(
            path: AppConstants.homeRoute,
            name: 'home',
            builder: (context, state) => const HomeScreen(),
          ),
          
          // Search Tab
          GoRoute(
            path: AppConstants.searchRoute,
            name: 'search',
            builder: (context, state) => const SearchScreen(),
          ),
          
          // Add Appointment Tab
          GoRoute(
            path: AppConstants.addAppointmentRoute,
            name: 'add-appointment',
            builder: (context, state) => const AddAppointmentScreen(),
          ),
          
          // Notifications Tab
          GoRoute(
            path: AppConstants.notificationsRoute,
            name: 'notifications',
            builder: (context, state) => const NotificationsScreen(),
          ),
          
          // Settings Tab
          GoRoute(
            path: AppConstants.settingsRoute,
            name: 'settings',
            builder: (context, state) => const SettingsScreen(),
          ),
        ],
      ),
      
      // Full Screen Routes (outside bottom navigation)
      GoRoute(
        path: '${AppConstants.profileRoute}/:username',
        name: 'profile',
        builder: (context, state) {
          final username = state.pathParameters['username']!;
          return ProfileScreen(username: username);
        },
      ),
      
      GoRoute(
        path: '${AppConstants.appointmentDetailsRoute}/:id',
        name: 'appointment-details',
        builder: (context, state) {
          final appointmentId = state.pathParameters['id']!;
          return AppointmentDetailsScreen(appointmentId: appointmentId);
        },
      ),

      // Demo Screen
      GoRoute(
        path: '/demo',
        name: 'demo',
        builder: (context, state) => const DemoScreen(),
      ),
    ],
    
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('خطأ'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'الصفحة غير موجودة',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'المسار: ${state.matchedLocation}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(AppConstants.homeRoute),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );
}
