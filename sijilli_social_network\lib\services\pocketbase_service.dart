import 'package:pocketbase/pocketbase.dart';
import '../models/models.dart';

class PocketbaseService {
  static PocketbaseService? _instance;
  static PocketbaseService get instance => _instance ??= PocketbaseService._();
  
  PocketbaseService._();

  late PocketBase _pb;
  
  // Initialize PocketBase connection
  void initialize({String? url}) {
    _pb = PocketBase(url ?? 'http://127.0.0.1:8090');
  }

  PocketBase get pb => _pb;

  // Auth methods
  Future<User?> signIn(String email, String password) async {
    try {
      final authData = await _pb.collection('users').authWithPassword(email, password);
      if (authData.record != null) {
        return User.fromJson(authData.record!.toJson());
      }
      return null;
    } catch (e) {
      print('Sign in error: $e');
      rethrow;
    }
  }

  Future<User?> signUp({
    required String email,
    required String password,
    required String username,
    String? displayName,
  }) async {
    try {
      final record = await _pb.collection('users').create(body: {
        'email': email,
        'password': password,
        'passwordConfirm': password,
        'username': username,
        'displayName': displayName ?? username,
        'role': 'user',
      });
      
      return User.from<PERSON>son(record.toJson());
    } catch (e) {
      print('Sign up error: $e');
      rethrow;
    }
  }

  void signOut() {
    _pb.authStore.clear();
  }

  bool get isAuthenticated => _pb.authStore.isValid;
  
  User? get currentUser {
    if (!isAuthenticated) return null;
    try {
      return User.fromJson(_pb.authStore.model?.toJson() ?? {});
    } catch (e) {
      return null;
    }
  }

  String? get currentUserId => _pb.authStore.model?.id;

  // Users methods
  Future<List<User>> searchUsers(String query) async {
    try {
      final records = await _pb.collection('users').getList(
        filter: 'username ~ "$query" || displayName ~ "$query"',
        sort: 'username',
      );
      
      return records.items.map((record) => User.fromJson(record.toJson())).toList();
    } catch (e) {
      print('Search users error: $e');
      return [];
    }
  }

  Future<User?> getUserById(String id) async {
    try {
      final record = await _pb.collection('users').getOne(id);
      return User.fromJson(record.toJson());
    } catch (e) {
      print('Get user error: $e');
      return null;
    }
  }

  Future<User?> getUserByUsername(String username) async {
    try {
      final records = await _pb.collection('users').getList(
        filter: 'username = "$username"',
        perPage: 1,
      );
      
      if (records.items.isNotEmpty) {
        return User.fromJson(records.items.first.toJson());
      }
      return null;
    } catch (e) {
      print('Get user by username error: $e');
      return null;
    }
  }

  // Appointments methods
  Future<List<Appointment>> getUserAppointments(String userId) async {
    try {
      final records = await _pb.collection('appointments').getList(
        filter: 'owner.id = "$userId" || guests.id ?= "$userId"',
        expand: 'owner,guests',
        sort: '-datetime',
      );
      
      return records.items.map((record) => Appointment.fromJson(record.toJson())).toList();
    } catch (e) {
      print('Get user appointments error: $e');
      return [];
    }
  }

  Future<Appointment?> createAppointment({
    required String title,
    required String privacy,
    required String region,
    String? building,
    required DateTime datetime,
    required String time,
    List<String>? guestIds,
  }) async {
    try {
      final record = await _pb.collection('appointments').create(body: {
        'title': title,
        'privacy': privacy,
        'region': region,
        'building': building,
        'datetime': datetime.toIso8601String(),
        'time': time,
        'owner': currentUserId,
        'guests': guestIds ?? [],
      });
      
      // Get the created appointment with expanded relations
      final expandedRecord = await _pb.collection('appointments').getOne(
        record.id,
        expand: 'owner,guests',
      );
      
      return Appointment.fromJson(expandedRecord.toJson());
    } catch (e) {
      print('Create appointment error: $e');
      rethrow;
    }
  }

  Future<Appointment?> getAppointmentById(String id) async {
    try {
      final record = await _pb.collection('appointments').getOne(
        id,
        expand: 'owner,guests',
      );
      return Appointment.fromJson(record.toJson());
    } catch (e) {
      print('Get appointment error: $e');
      return null;
    }
  }

  Future<bool> deleteAppointment(String id) async {
    try {
      await _pb.collection('appointments').delete(id);
      return true;
    } catch (e) {
      print('Delete appointment error: $e');
      return false;
    }
  }
}
