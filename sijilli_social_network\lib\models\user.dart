class User {
  final String id;
  final String username;
  final String email;
  final bool emailVisibility;
  final bool verified;
  final UserRole role;
  final DateTime created;
  final DateTime updated;

  // Additional profile fields (to be added later)
  String? displayName;
  String? avatar;
  String? bio;
  String? externalLink;

  User({
    required this.id,
    required this.username,
    required this.email,
    required this.emailVisibility,
    required this.verified,
    required this.role,
    required this.created,
    required this.updated,
    this.displayName,
    this.avatar,
    this.bio,
    this.externalLink,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      emailVisibility: json['emailVisibility'] ?? false,
      verified: json['verified'] ?? false,
      role: UserRole.fromString(json['role'] ?? 'user'),
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
      displayName: json['displayName'],
      avatar: json['avatar'],
      bio: json['bio'],
      externalLink: json['externalLink'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'emailVisibility': emailVisibility,
      'verified': verified,
      'role': role.toString(),
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
      'displayName': displayName,
      'avatar': avatar,
      'bio': bio,
      'externalLink': externalLink,
    };
  }

  User copyWith({
    String? id,
    String? username,
    String? email,
    bool? emailVisibility,
    bool? verified,
    UserRole? role,
    DateTime? created,
    DateTime? updated,
    String? displayName,
    String? avatar,
    String? bio,
    String? externalLink,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      emailVisibility: emailVisibility ?? this.emailVisibility,
      verified: verified ?? this.verified,
      role: role ?? this.role,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      displayName: displayName ?? this.displayName,
      avatar: avatar ?? this.avatar,
      bio: bio ?? this.bio,
      externalLink: externalLink ?? this.externalLink,
    );
  }

  @override
  String toString() {
    return 'User(id: $id, username: $username, email: $email, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum UserRole {
  user,
  approved,
  admin;

  static UserRole fromString(String role) {
    switch (role.toLowerCase()) {
      case 'user':
        return UserRole.user;
      case 'approved':
        return UserRole.approved;
      case 'admin':
        return UserRole.admin;
      default:
        return UserRole.user;
    }
  }

  @override
  String toString() {
    return name;
  }

  bool get isAdmin => this == UserRole.admin;
  bool get isApproved => this == UserRole.approved || this == UserRole.admin;
  bool get isUser => this == UserRole.user;
}
