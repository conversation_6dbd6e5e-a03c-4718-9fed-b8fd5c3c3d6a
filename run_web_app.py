#!/usr/bin/env python3
"""
Simple web server to run Sijilli demo app
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

PORT = 8080

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

def main():
    # Change to the directory containing demo.html
    os.chdir(Path(__file__).parent)
    
    # Check if demo.html exists
    if not os.path.exists('demo.html'):
        print("Error: demo.html not found!")
        sys.exit(1)
    
    # Start the server
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"🚀 Starting Sijilli Web App Server...")
        print(f"📱 Server running at: http://localhost:{PORT}")
        print(f"🌐 Opening demo.html in browser...")
        print(f"🛑 Press Ctrl+C to stop the server")
        
        # Open browser
        webbrowser.open(f'http://localhost:{PORT}/demo.html')
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped.")

if __name__ == "__main__":
    main()
