import 'user.dart';

class Follow {
  final String id;
  final User follower;
  final User following;
  final DateTime created;
  final DateTime updated;

  Follow({
    required this.id,
    required this.follower,
    required this.following,
    required this.created,
    required this.updated,
  });

  factory Follow.fromJson(Map<String, dynamic> json) {
    return Follow(
      id: json['id'] ?? '',
      follower: User.from<PERSON><PERSON>(json['expand']?['follower'] ?? {}),
      following: User.from<PERSON>son(json['expand']?['following'] ?? {}),
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'follower': follower.id,
      'following': following.id,
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  Follow copyWith({
    String? id,
    User? follower,
    User? following,
    DateTime? created,
    DateTime? updated,
  }) {
    return Follow(
      id: id ?? this.id,
      follower: follower ?? this.follower,
      following: following ?? this.following,
      created: created ?? this.created,
      updated: updated ?? this.updated,
    );
  }

  @override
  String toString() {
    return 'Follow(id: $id, follower: ${follower.username}, following: ${following.username})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Follow && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class FollowRelationship {
  final bool isFollowing;
  final bool isFollower;
  final bool isMutual;

  FollowRelationship({
    required this.isFollowing,
    required this.isFollower,
    required this.isMutual,
  });

  String get relationshipType {
    if (isMutual) return 'صديق';
    if (isFollowing) return 'أتابعه';
    if (isFollower) return 'يتابعني';
    return 'لا توجد علاقة';
  }

  String get actionText {
    if (isFollowing) return 'إلغاء المتابعة';
    return 'متابعة';
  }
}
