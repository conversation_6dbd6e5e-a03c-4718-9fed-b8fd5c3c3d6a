<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>سِجِلّي - تطبيق المواعيد</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            overflow-x: hidden;
        }

        /* Mobile Container */
        .app-container {
            max-width: 414px;
            margin: 0 auto;
            min-height: 100vh;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }

        /* Mobile-first responsive design */
        @media (max-width: 480px) {
            .app-container {
                max-width: 100%;
                box-shadow: none;
            }
        }

        @media (min-width: 481px) {
            body {
                background: #e9ecef;
                padding: 20px 0;
            }

            .app-container {
                border-radius: 20px;
                overflow: hidden;
            }
        }

        /* Auth Screens */
        .auth-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow-y: auto;
        }

        .login-screen {
            display: flex;
        }

        .register-screen {
            display: none;
        }

        .auth-container {
            width: 100%;
            max-width: 320px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .app-logo {
            font-size: 42px;
            font-weight: bold;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            text-align: center;
        }

        .auth-subtitle {
            font-size: 14px;
            margin-bottom: 32px;
            opacity: 0.9;
            text-align: center;
            line-height: 1.4;
        }

        .auth-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 24px;
            text-align: center;
        }

        .step-indicator {
            text-align: center;
            margin-bottom: 24px;
        }

        .step-text {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 8px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #fff;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .form-group {
            margin-bottom: 20px;
            width: 100%;
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            text-align: right;
            box-sizing: border-box;
            -webkit-appearance: none;
            appearance: none;
            transition: all 0.3s ease;
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .form-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.6);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
        }

        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.7);
            pointer-events: none;
        }

        .input-hint {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 4px;
            text-align: right;
        }

        .auth-btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 16px;
            box-sizing: border-box;
            -webkit-appearance: none;
            appearance: none;
            position: relative;
            overflow: hidden;
        }

        .auth-btn-primary {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .auth-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .auth-btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .auth-btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .auth-btn:disabled {
            background: rgba(255, 255, 255, 0.3);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .auth-btn:active {
            transform: translateY(0);
        }

        .btn-group {
            display: flex;
            gap: 12px;
            margin-top: 8px;
        }

        .btn-group .auth-btn {
            flex: 1;
            margin-bottom: 0;
        }

        .auth-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-size: 14px;
            margin-top: 20px;
            display: block;
            text-align: center;
            transition: color 0.3s ease;
        }

        .auth-link:hover {
            color: white;
            text-decoration: underline;
        }

        .error-message {
            background: #dc3545;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
            max-width: 300px;
        }

        /* Main App */
        .main-app {
            display: none;
            min-height: 100vh;
            padding-bottom: 80px;
            background: #f8f9fa;
        }

        .app-bar {
            background: #495057;
            color: white;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .app-bar span {
            font-size: 18px;
            font-weight: 600;
        }

        .content {
            padding: 16px;
            min-height: calc(100vh - 140px);
        }

        .connection-status {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 14px;
        }

        .connection-status.error {
            background: #f8d7da;
            color: #721c24;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            max-width: 414px;
            width: 100%;
            background: white;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: space-around;
            padding: 8px 0 12px 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        @media (max-width: 480px) {
            .bottom-nav {
                left: 0;
                transform: none;
                max-width: 100%;
            }
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 6px 8px;
            color: #6c757d;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            border-radius: 8px;
            min-width: 50px;
        }

        .nav-item.active {
            color: #495057;
            background: rgba(73, 80, 87, 0.1);
        }

        .nav-item:hover {
            background: rgba(73, 80, 87, 0.05);
        }

        .nav-icon {
            margin-bottom: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: #495057;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            z-index: 999;
        }

        .fab:hover {
            background: #343a40;
            transform: scale(1.05);
        }

        .fab:active {
            transform: scale(0.95);
        }

        @media (min-width: 481px) {
            .fab {
                right: calc(50% - 187px);
            }
        }

        /* Appointments List */
        .appointments-container {
            max-width: 600px;
            margin: 0 auto;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        /* Notification */
        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%) translateY(-100px);
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            transition: transform 0.3s ease;
            max-width: 300px;
            text-align: center;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        @media (max-width: 480px) {
            .notification {
                left: 20px;
                right: 20px;
                transform: translateY(-100px);
                max-width: none;
            }
        }

        .notification.show {
            transform: translateX(-50%) translateY(0);
        }

        @media (max-width: 480px) {
            .notification.show {
                transform: translateY(0);
            }
        }

        .notification.success {
            background: #28a745;
        }

        .notification.error {
            background: #dc3545;
        }

        .notification.info {
            background: #17a2b8;
        }

        /* Tab Content Styles */
        .tab-content {
            display: none;
            padding: 20px 0;
            min-height: calc(100vh - 140px);
            overflow-y: auto;
        }

        .tab-content.active {
            display: block;
        }

        /* Settings Styles */

        .settings-container, .profile-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .settings-container h2, .profile-container h2 {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
            font-size: 24px;
        }

        .settings-section {
            background: white;
            border-radius: 12px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .settings-section h3 {
            margin: 0;
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
            color: #333;
            font-size: 16px;
        }

        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-item:hover {
            background-color: #f8f9fa;
        }

        .setting-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .setting-icon {
            font-size: 20px;
            margin-right: 15px;
            width: 24px;
            text-align: center;
        }

        .setting-details {
            flex: 1;
        }

        .setting-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
        }

        .setting-description {
            font-size: 14px;
            color: #666;
        }

        .setting-arrow {
            font-size: 18px;
            color: #999;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #4CAF50;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .setting-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            color: #333;
            min-width: 120px;
        }

        .logout-setting {
            color: #f44336 !important;
        }

        .logout-setting .setting-title {
            color: #f44336;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            max-width: 500px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .hijri-correction-panel {
            padding: 20px;
        }

        .date-radio-group {
            margin: 15px 0;
        }

        .date-option {
            display: flex;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            border: 2px solid #eee;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .date-option:hover {
            border-color: #4CAF50;
            background: #f8fff8;
        }

        .date-option input[type="radio"] {
            margin-right: 12px;
        }

        .date-option.selected {
            border-color: #4CAF50;
            background: #f8fff8;
        }

        .date-text {
            font-weight: 600;
            margin-right: 10px;
        }

        .correction-indicator {
            font-size: 14px;
            color: #666;
        }

        .correction-actions {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }

        .apply-btn, .reset-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
        }

        .apply-btn {
            background: #4CAF50;
            color: white;
        }

        .reset-btn {
            background: #f0f0f0;
            color: #333;
        }

        .correction-preview {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .preview-appointments {
            margin-top: 10px;
        }

        .preview-item {
            padding: 10px;
            margin: 8px 0;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #4CAF50;
        }

        /* Profile Styles */
        .profile-header {
            display: flex;
            align-items: center;
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .profile-avatar {
            position: relative;
            margin-right: 20px;
        }

        .profile-avatar img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
        }

        .avatar-edit-btn {
            position: absolute;
            bottom: 0;
            right: 0;
            background: #4CAF50;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 12px;
            cursor: pointer;
            color: white;
        }

        .profile-info h3 {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 20px;
        }

        .profile-info p {
            margin: 2px 0;
            color: #666;
            font-size: 14px;
        }

        .profile-stats {
            display: flex;
            background: white;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .stat-item {
            flex: 1;
            text-align: center;
            padding: 20px;
            border-right: 1px solid #f0f0f0;
        }

        .stat-item:last-child {
            border-right: none;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
        }

        .profile-sections {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .profile-section {
            margin-bottom: 30px;
        }

        .profile-section:last-child {
            margin-bottom: 0;
        }

        .profile-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }

        .profile-field {
            margin-bottom: 15px;
        }

        .profile-field label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 600;
            font-size: 14px;
        }

        .profile-field input,
        .profile-field textarea,
        .profile-field select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            color: #333;
            background: #f9f9f9;
            transition: border-color 0.2s, background-color 0.2s;
        }

        .profile-field input:focus,
        .profile-field textarea:focus,
        .profile-field select:focus {
            outline: none;
            border-color: #4CAF50;
            background: white;
        }

        .profile-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #f0f0f0;
        }

        .save-btn, .reset-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .save-btn {
            background: #4CAF50;
            color: white;
        }

        .save-btn:hover {
            background: #45a049;
        }

        .reset-btn {
            background: #f0f0f0;
            color: #333;
        }

        .reset-btn:hover {
            background: #e0e0e0;
        }

        /* Profile Screen */
        .profile-screen {
            display: none;
            min-height: 100vh;
            background: #f8f9fa;
        }

        .back-btn, .edit-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: background 0.2s ease;
        }

        .back-btn:hover, .edit-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .profile-content {
            padding: 0 0 100px 0;
        }

        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px 20px;
            text-align: center;
            color: white;
            position: relative;
        }

        .profile-avatar-section {
            position: relative;
            display: inline-block;
            margin-bottom: 20px;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            border: 4px solid rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .avatar-placeholder-large {
            font-size: 40px;
            color: rgba(255, 255, 255, 0.7);
        }

        .avatar-edit-overlay {
            position: absolute;
            bottom: 0;
            right: 0;
            background: #28a745;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .avatar-edit-overlay:hover {
            background: #218838;
            transform: scale(1.1);
        }

        .avatar-edit-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 0;
        }

        .profile-info {
            text-align: center;
        }

        .profile-name {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .profile-username {
            font-size: 16px;
            opacity: 0.8;
            margin-bottom: 4px;
        }

        .profile-email {
            font-size: 14px;
            opacity: 0.7;
        }

        .profile-details {
            padding: 20px;
        }

        .profile-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #495057;
            margin: 30px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #e9ecef;
        }

        .profile-field {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        .field-label {
            font-size: 14px;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .field-content {
            position: relative;
        }

        .field-value {
            font-size: 16px;
            color: #495057;
            min-height: 20px;
        }

        .field-value.empty {
            color: #adb5bd;
            font-style: italic;
        }

        .field-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            background: #f8f9fa;
            color: #495057;
            text-align: right;
            box-sizing: border-box;
            transition: all 0.3s ease;
        }

        .field-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .edit-actions {
            padding: 20px;
            display: flex;
            gap: 12px;
            position: fixed;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            max-width: 374px;
            width: calc(100% - 40px);
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        @media (max-width: 480px) {
            .edit-actions {
                left: 20px;
                right: 20px;
                transform: none;
                max-width: none;
                width: calc(100% - 40px);
            }
        }

        .edit-actions .auth-btn {
            flex: 1;
            margin-bottom: 0;
        }

        .profile-actions {
            padding: 20px;
            margin-top: 20px;
        }

        .action-btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-sizing: border-box;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        }

        .logout-btn:hover {
            background: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
        }

        .logout-btn:active {
            transform: translateY(0);
        }

        /* Add Appointment Screen */
        .add-appointment-screen {
            display: none;
            min-height: 100vh;
            background: #f8f9fa;
        }

        .add-appointment-content {
            padding: 0 0 100px 0;
        }

        .appointment-form {
            padding: 20px;
        }

        .form-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 18px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid #f8f9fa;
        }

        .form-row {
            display: flex;
            gap: 12px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            background: white;
            color: #495057;
            text-align: right;
            box-sizing: border-box;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-input::placeholder {
            color: #adb5bd;
        }

        /* Title and Privacy Row */
        .title-privacy-row {
            display: flex;
            gap: 16px;
            align-items: flex-start;
        }

        .title-group {
            flex: 2;
        }

        .privacy-group {
            flex: 1;
            min-width: 140px;
        }

        @media (max-width: 480px) {
            .title-privacy-row {
                flex-direction: column;
                gap: 20px;
            }

            .title-group,
            .privacy-group {
                flex: 1;
                min-width: auto;
            }
        }

        /* Compact Privacy Options */
        .privacy-options-compact {
            display: flex;
            gap: 8px;
            margin-bottom: 4px;
        }

        .privacy-option-compact {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            min-height: 40px;
        }

        .privacy-option-compact.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .privacy-option-compact input[type="radio"] {
            display: none;
        }

        .privacy-content-compact {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
        }

        .privacy-icon-small {
            font-size: 14px;
        }

        .privacy-title-small {
            font-weight: 600;
            color: #495057;
        }

        .privacy-hint {
            font-size: 10px;
            color: #6c757d;
            text-align: center;
            margin-top: 4px;
            line-height: 1.2;
        }

        .input-hint {
            font-size: 11px;
            color: #6c757d;
            margin-top: 4px;
        }

        /* Calendar Type Selection */
        .calendar-options {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }

        .calendar-option {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .calendar-option.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .calendar-option input[type="radio"] {
            display: none;
        }

        .calendar-content {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .calendar-icon {
            font-size: 18px;
        }

        .calendar-title {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        /* Date Input Section */
        .date-input-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        @media (max-width: 480px) {
            .date-input-section {
                grid-template-columns: 1fr;
            }
        }

        .date-display-section {
            margin-top: 16px;
        }

        .date-conversion {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 16px;
            margin-top: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .conversion-label {
            font-size: 12px;
            color: #6c757d;
            font-weight: 500;
        }

        .conversion-value {
            font-size: 14px;
            color: #495057;
            font-weight: 600;
        }

        .conversion-value.highlight {
            color: #667eea;
        }

        /* Weekday Display */
        .weekday-display {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 8px;
            padding: 12px 16px;
            margin-top: 12px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }

        .weekday-label {
            font-size: 12px;
            opacity: 0.9;
            font-weight: 500;
        }

        .weekday-value {
            font-size: 14px;
            font-weight: 600;
        }

        .weekday-value.highlight {
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        /* Hijri Date Inputs */
        select.form-input {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: left 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-left: 32px;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            padding-left: 40px !important;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            pointer-events: none;
        }

        .search-results {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: white;
            margin-top: 8px;
            display: none;
        }

        .search-result-item {
            padding: 12px;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: background 0.2s ease;
        }

        .search-result-item:hover {
            background: #f8f9fa;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .result-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            overflow: hidden;
        }

        .result-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .result-info {
            flex: 1;
        }

        .result-name {
            font-weight: 600;
            color: #212529;
            font-size: 14px;
        }

        .result-username {
            color: #495057;
            font-size: 12px;
        }

        .selected-guests {
            margin-top: 20px;
        }

        .guests-header {
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 12px;
        }

        .guests-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .no-guests {
            color: #adb5bd;
            font-style: italic;
            font-size: 14px;
            text-align: center;
            padding: 20px;
        }

        .guest-chip {
            display: flex;
            align-items: center;
            gap: 8px;
            background: #667eea;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .guest-chip .remove-guest {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s ease;
        }

        .guest-chip .remove-guest:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: 30px;
            padding: 20px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        .form-actions .auth-btn {
            flex: 1;
            margin-bottom: 0;
        }

        /* Appointments List */
        .appointments-header {
            padding: 16px 20px 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 12px;
        }

        .appointments-filter {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            flex: 1;
        }

        .refresh-btn {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #6c757d;
            margin-bottom: 16px;
        }

        .refresh-btn:hover {
            border-color: #667eea;
            color: #667eea;
            transform: rotate(180deg);
        }

        .filter-btn {
            flex: 1;
            padding: 8px 16px;
            border: 2px solid #e9ecef;
            border-radius: 20px;
            background: white;
            color: #6c757d;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn.active {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .filter-btn:hover:not(.active) {
            border-color: #667eea;
            color: #667eea;
        }

        .loading-state {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-state h3 {
            margin: 0 0 8px 0;
            color: #495057;
            font-size: 18px;
        }

        .empty-state p {
            margin: 0 0 24px 0;
            font-size: 14px;
            line-height: 1.4;
        }

        .appointments-list {
            padding: 0 0 20px 0;
        }

        /* Appointment Card - Simplified Design */

        .appointment-content-box {
            background: #8dbcdf;               /* لون أزرق فاتح */
            border-radius: 15px;               /* زوايا دائرية 15px */
            padding: 2px;                      /* padding 2px من جميع الجهات */
            box-sizing: border-box;

            width: 100%;                       /* عرض 100% */
            height: 140px;                     /* ارتفاع 140px */
            margin-bottom: 16px;               /* فصل بين البطاقات */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);  /* ظل خفيف */

            /* لجعل الحاوية الداخلية في الأسفل */
            display: flex;
            flex-direction: column;
            justify-content: flex-end;         /* محاذاة في الأسفل */
        }

        .inner-container {
            height: 110px;                     /* ارتفاع 110px */
            background: #f9f9f9;               /* خلفية مؤقتة للرؤية */
            border-radius: 12px;               /* زوايا دائرية 12px - زيادة من 10px */
            padding: 4px;                      /* padding 4px من جميع الجهات */
            box-sizing: border-box;            /* تأكيد أن البادينغ لا يسبب تمدد */
            position: relative;                /* للصورة المطلقة */
        }

        .owner-avatar-test {
            position: absolute;
            top: 4px;                          /* مسافة 4px من الأعلى (نفس الـ padding) */
            right: 4px;                        /* مسافة 4px من اليمين (نفس الـ padding) */
            width: 48px;                       /* عرض الصورة */
            height: 48px;                      /* ارتفاع الصورة */
            border-radius: 50%;                /* دائرية */
            object-fit: cover;                 /* تناسب الصورة */
            /* بدون إطار للاختبار */
        }

        .appointment-title {
            position: absolute;
            top: 16px;                         /* أنزل أكثر (كان 8px، أصبح 16px) */
            left: 4px;                         /* مسافة 4px من اليسار */
            right: 60px;                       /* مسافة للصورة (48px + 4px + 8px هامش) */
            font-size: 18px;                   /* حجم خط كبير */
            font-weight: bold;                 /* عريض */
            color: #333;                       /* لون داكن لافت */
            margin: 0;                         /* إزالة المارجن الافتراضي */
            line-height: 24px;                 /* ارتفاع سطر مناسب للتايتل */
            text-align: right;                 /* محاذاة يمين للعربية */
            white-space: nowrap;               /* عدم كسر السطر */
            overflow: hidden;                  /* إخفاء النص الزائد */
            text-overflow: ellipsis;           /* نقاط في نهاية النص الطويل */
        }

        .appointment-location {
            position: absolute;
            top: 48px;                         /* أنزل أكثر (كان 38px، أصبح 48px) */
            left: 4px;                         /* نفس البادئة من اليسار */
            right: 60px;                       /* نفس المحاذاة مع التايتل */
            font-size: 12px;                   /* خط صغير */
            font-weight: normal;               /* وزن عادي */
            color: #666;                       /* لون رمادي */
            margin: 0;                         /* إزالة المارجن الافتراضي */
            line-height: 16px;                 /* ارتفاع سطر مناسب */
            text-align: right;                 /* محاذاة يمين للعربية */
            white-space: nowrap;               /* عدم كسر السطر */
            overflow: hidden;                  /* إخفاء النص الزائد */
            text-overflow: ellipsis;           /* نقاط في نهاية النص الطويل */
        }

        .appointment-datetime {
            position: absolute;
            top: 68px;                         /* أسفل الأدريس (48px + 16px + 4px) */
            left: 4px;                         /* نفس البادئة من اليسار */
            right: 60px;                       /* نفس المحاذاة مع الأدريس */
            font-size: 12px;                   /* نفس حجم الأدريس */
            font-weight: normal;               /* وزن عادي */
            color: #666;                       /* نفس لون الأدريس */
            margin: 0;                         /* إزالة المارجن الافتراضي */
            line-height: 16px;                 /* نفس ارتفاع سطر الأدريس */
            text-align: right;                 /* محاذاة يمين للعربية */
            white-space: nowrap;               /* عدم كسر السطر */
            overflow: hidden;                  /* إخفاء النص الزائد */
            text-overflow: ellipsis;           /* نقاط في نهاية النص الطويل */
        }





        .appointment-status-bar {
            /* شريط الحالة - جزء من الحاوية الكبيرة */
            position: absolute;
            top: 0;
            left: 8px;
            right: 8px;
            height: 25px;
            background: transparent; /* شفاف - جزء من الحاوية الزرقاء */
            border-radius: 8px 8px 0 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .appointment-avatars {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .avatar-container {
            position: relative;
            width: 50px;
            height: 50px;
        }

        .avatar-image {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }

        .avatar-owner {
            border: 3px solid #00a000; /* أخضر - نشط */
        }

        .avatar-guest {
            border: 3px solid #ff0000; /* أحمر - محذوف */
        }

        .avatar-status {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .status-active {
            background: #00a000;
        }

        .status-deleted {
            background: #ff0000;
        }

        .appointment-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .appointment-title {
            font-size: 18px;
            font-weight: 600;
            color: #212529;
            margin: 0;
            flex: 1;
            margin-left: 12px;
        }

        .appointment-privacy {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #6c757d;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 12px;
        }

        .appointment-details {
            display: flex;
            flex-direction: column;
            gap: 6px;
            margin-bottom: 0;
        }

        .appointment-detail {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #495057;
            line-height: 1.4;
        }

        .appointment-detail span {
            word-break: break-word;
        }

        .appointment-detail svg {
            color: #6c757d;
            flex-shrink: 0;
        }

        .appointment-guests {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 12px;
        }

        .guests-avatars {
            display: flex;
            gap: -8px;
        }

        .guest-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #6c757d;
            color: white;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
            margin-right: -8px;
            overflow: hidden;
        }

        .guest-avatar:first-child {
            margin-right: 0;
        }

        .guest-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .guests-count {
            font-size: 12px;
            color: #6c757d;
            margin-right: 4px;
        }

        /* Owner with Details Layout */
        .owner-with-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .owner-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .details-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .details-btn:hover {
            background: #5a6fd8;
        }

        /* Dual Date Display */
        .dual-date {
            font-size: 14px !important;
            color: #495057 !important;
            font-weight: normal !important;
            line-height: 1.4;
        }

        .dual-date .separator {
            color: #667eea;
            font-weight: bold;
            margin: 0 8px;
        }

        .secondary-date {
            color: #6c757d !important;
            font-size: 12px !important;
            font-weight: normal !important;
            opacity: 0.8;
        }

        .action-btn-small {
            padding: 6px 12px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            background: white;
            color: #495057;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn-small:hover {
            background: #f8f9fa;
            border-color: #667eea;
            color: #667eea;
        }

        .action-btn-small.primary {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .action-btn-small.primary:hover {
            background: #5a6fd8;
        }

        /* New Card Content Styles */
        .appointment-content {
            flex: 1;
        }

        .appointment-card .appointment-title {
            font-size: 18px;
            font-weight: bold;
            color: white !important; /* أبيض على الخلفية البرتقالية */
            margin-bottom: 8px;
            text-align: right;
        }

        .appointment-card .appointment-location {
            font-size: 14px;
            color: #fff !important; /* أبيض على الخلفية البرتقالية */
            margin-bottom: 8px;
            text-align: right;
            opacity: 0.9;
        }

        .appointment-card .appointment-datetime {
            font-size: 16px;
            color: #fff !important; /* أبيض على الخلفية البرتقالية */
            font-weight: 600;
            margin-bottom: 8px;
            text-align: right;
        }

        .appointment-card .appointment-guest-name {
            font-size: 14px;
            color: #fff !important; /* أبيض على الخلفية البرتقالية */
            margin-bottom: 10px;
            text-align: right;
            opacity: 0.9;
        }

        .appointment-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.3);
        }

        .details-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .details-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .countdown-timer {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
            color: white;
            font-weight: 600;
        }

        .countdown-urgent {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }

        .countdown-critical {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }


    </style>
</head>
<body>
    <div class="app-container">
        <!-- Login Screen -->
        <div class="auth-screen login-screen" id="loginScreen">
            <div class="auth-container">
                <div class="app-logo">سِجِلّي</div>
                <div class="auth-subtitle">تطبيق إدارة المواعيد الذكي</div>

                <div class="auth-title">تسجيل الدخول</div>

                <div id="errorMessage" class="error-message" style="display: none;"></div>

                <div class="form-group">
                    <input type="email" class="form-input" placeholder="البريد الإلكتروني"
                           id="emailInput" value="<EMAIL>">
                    <div class="input-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                            <polyline points="22,6 12,13 2,6"/>
                        </svg>
                    </div>
                </div>

                <div class="form-group">
                    <input type="password" class="form-input" placeholder="كلمة المرور"
                           id="passwordInput" value="123456789">
                    <div class="input-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                            <circle cx="12" cy="16" r="1"/>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                        </svg>
                    </div>
                </div>

                <button class="auth-btn auth-btn-primary" id="loginBtn">
                    تسجيل الدخول
                </button>

                <a href="#" class="auth-link" id="registerLink">
                    ليس لديك حساب؟ إنشاء حساب جديد
                </a>
            </div>
        </div>

        <!-- Register Screen -->
        <div class="auth-screen register-screen" id="registerScreen">
            <div class="auth-container">
                <div class="app-logo">سِجِلّي</div>
                <div class="auth-subtitle">انضم إلى مجتمع إدارة المواعيد</div>

                <!-- Step 1: Basic Info -->
                <div id="registerStep1" class="register-step">
                    <div class="auth-title">إنشاء حساب جديد</div>

                    <div class="step-indicator">
                        <div class="step-text">الخطوة 1 من 2: المعلومات الأساسية</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 50%;"></div>
                        </div>
                    </div>

                    <div id="registerErrorMessage" class="error-message" style="display: none;"></div>

                    <div class="form-group">
                        <input type="text" class="form-input" placeholder="اسم المستخدم (إنجليزي)"
                               id="registerUsername" pattern="[a-zA-Z0-9._]+">
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                                <circle cx="12" cy="7" r="4"/>
                            </svg>
                        </div>
                        <div class="input-hint">مثال: ahmed.ali، user_123</div>
                    </div>

                    <div class="form-group">
                        <input type="email" class="form-input" placeholder="البريد الإلكتروني"
                               id="registerEmail">
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                <polyline points="22,6 12,13 2,6"/>
                            </svg>
                        </div>
                    </div>

                    <div class="form-group">
                        <input type="password" class="form-input" placeholder="كلمة المرور (8 أحرف على الأقل)"
                               id="registerPassword" minlength="8">
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                                <circle cx="12" cy="16" r="1"/>
                                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                            </svg>
                        </div>
                    </div>

                    <div class="form-group">
                        <input type="password" class="form-input" placeholder="تأكيد كلمة المرور"
                               id="registerPasswordConfirm">
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                                <path d="M9 16l2 2 4-4"/>
                            </svg>
                        </div>
                    </div>

                    <button class="auth-btn auth-btn-primary" id="nextStepBtn">
                        التالي
                    </button>

                    <a href="#" class="auth-link" id="backToLoginLink">
                        لديك حساب بالفعل؟ تسجيل الدخول
                    </a>
                </div>

                <!-- Step 2: Personal Info -->
                <div id="registerStep2" class="register-step" style="display: none;">
                    <div class="auth-title">المعلومات الشخصية</div>

                    <div class="step-indicator">
                        <div class="step-text">الخطوة 2 من 2: معلومات إضافية (اختيارية)</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%;"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <input type="text" class="form-input" placeholder="اسم العرض"
                               id="registerDisplayName" maxlength="50">
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4z"/>
                                <path d="M16 18v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            </svg>
                        </div>
                        <div class="input-hint">كما تريد أن يظهر للآخرين</div>
                    </div>

                    <div class="form-group">
                        <input type="text" class="form-input" placeholder="المهنة أو التخصص"
                               id="registerJob" maxlength="100">
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                                <line x1="8" y1="21" x2="16" y2="21"/>
                                <line x1="12" y1="17" x2="12" y2="21"/>
                            </svg>
                        </div>
                        <div class="input-hint">مثال: مطور، مصمم، طبيب</div>
                    </div>

                    <div class="form-group">
                        <input type="tel" class="form-input" placeholder="رقم الهاتف"
                               id="registerPhone" maxlength="20">
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                            </svg>
                        </div>
                        <div class="input-hint">مثال: +965-12345678</div>
                    </div>

                    <div class="btn-group">
                        <button class="auth-btn auth-btn-secondary" id="prevStepBtn">
                            السابق
                        </button>
                        <button class="auth-btn auth-btn-primary" id="registerBtn">
                            إنشاء الحساب
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Screen -->
        <div class="profile-screen" id="profileScreen" style="display: none;">
            <div class="app-bar">
                <button class="back-btn" onclick="closeProfile()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m15 18-6-6 6-6"/>
                    </svg>
                </button>
                <span>الملف الشخصي</span>
                <button class="edit-btn" id="editProfileBtn" onclick="toggleEdit()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                    </svg>
                </button>
            </div>

            <div class="profile-content">
                <!-- Profile Header -->
                <div class="profile-header">
                    <div class="profile-avatar-section">
                        <div class="profile-avatar" id="profileAvatar">
                            <img id="profileAvatarImg" src="" alt="الصورة الشخصية" style="display: none;">
                            <div id="profileAvatarPlaceholder" class="avatar-placeholder-large">👤</div>
                        </div>
                        <div class="avatar-edit-overlay" id="avatarEditOverlay" style="display: none;">
                            <input type="file" id="avatarInput" accept="image/*" style="display: none;">
                            <button class="avatar-edit-btn" onclick="document.getElementById('avatarInput').click()">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                                    <circle cx="12" cy="13" r="4"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <div class="profile-info">
                        <div class="profile-name" id="profileDisplayName">اسم المستخدم</div>
                        <div class="profile-username" id="profileUsername">@username</div>
                        <div class="profile-email" id="profileEmail"><EMAIL></div>
                    </div>
                </div>

                <!-- Profile Details -->
                <div class="profile-details">
                    <!-- Display Name -->
                    <div class="profile-field">
                        <div class="field-label">اسم العرض</div>
                        <div class="field-content">
                            <div class="field-value" id="displayNameView">لم يتم تعيين اسم عرض</div>
                            <input type="text" class="field-input" id="displayNameEdit" style="display: none;"
                                   placeholder="اسمك كما تريد أن يظهر للآخرين" maxlength="50">
                        </div>
                    </div>

                    <!-- Job -->
                    <div class="profile-field">
                        <div class="field-label">المهنة / التخصص</div>
                        <div class="field-content">
                            <div class="field-value" id="jobView">لم يتم تحديد المهنة</div>
                            <input type="text" class="field-input" id="jobEdit" style="display: none;"
                                   placeholder="مثال: مطور برمجيات، مصمم، طبيب" maxlength="100">
                        </div>
                    </div>

                    <!-- Phone -->
                    <div class="profile-field">
                        <div class="field-label">رقم الهاتف</div>
                        <div class="field-content">
                            <div class="field-value" id="phoneView">لم يتم إضافة رقم هاتف</div>
                            <input type="tel" class="field-input" id="phoneEdit" style="display: none;"
                                   placeholder="مثال: +965-12345678" maxlength="20">
                        </div>
                    </div>

                    <!-- Bio -->
                    <div class="profile-field">
                        <div class="field-label">النبذة الشخصية</div>
                        <div class="field-content">
                            <div class="field-value" id="bioView">لم يتم إضافة نبذة شخصية</div>
                            <textarea class="field-input" id="bioEdit" style="display: none;"
                                      placeholder="اكتب نبذة مختصرة عن نفسك..." maxlength="500" rows="3"></textarea>
                        </div>
                    </div>

                    <!-- Social Links -->
                    <div class="profile-section-title">روابط التواصل الاجتماعي</div>

                    <!-- Instagram -->
                    <div class="profile-field">
                        <div class="field-label">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="2" width="20" height="20" rx="5" ry="5"/>
                                <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"/>
                                <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"/>
                            </svg>
                            Instagram
                        </div>
                        <div class="field-content">
                            <div class="field-value" id="instagramView">لم يتم إضافة رابط</div>
                            <input type="text" class="field-input" id="instagramEdit" style="display: none;"
                                   placeholder="اسم المستخدم في Instagram" maxlength="50">
                        </div>
                    </div>

                    <!-- YouTube -->
                    <div class="profile-field">
                        <div class="field-label">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"/>
                                <polygon points="9.75,15.02 15.5,11.75 9.75,8.48"/>
                            </svg>
                            YouTube
                        </div>
                        <div class="field-content">
                            <div class="field-value" id="youtubeView">لم يتم إضافة رابط</div>
                            <input type="text" class="field-input" id="youtubeEdit" style="display: none;"
                                   placeholder="اسم القناة في YouTube" maxlength="50">
                        </div>
                    </div>
                </div>

                <!-- Edit Actions -->
                <div class="edit-actions" id="editActions" style="display: none;">
                    <button class="auth-btn auth-btn-secondary" onclick="cancelEdit()">إلغاء</button>
                    <button class="auth-btn auth-btn-primary" onclick="saveProfile()" id="saveProfileBtn">حفظ التغييرات</button>
                </div>

                <!-- Profile Actions -->
                <div class="profile-actions">
                    <button class="action-btn logout-btn" onclick="logout()">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                            <polyline points="16,17 21,12 16,7"/>
                            <line x1="21" y1="12" x2="9" y2="12"/>
                        </svg>
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </div>

        <!-- Add Appointment Screen -->
        <div class="add-appointment-screen" id="addAppointmentScreen" style="display: none;">
            <div class="app-bar">
                <button class="back-btn" onclick="closeAddAppointment()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m15 18-6-6 6-6"/>
                    </svg>
                </button>
                <span>إضافة موعد جديد</span>
                <div style="width: 40px;"></div>
            </div>

            <div class="add-appointment-content">
                <form id="addAppointmentForm" class="appointment-form">
                    <!-- Title -->
                    <div class="form-section">
                        <div class="section-title">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                                <line x1="16" y1="13" x2="8" y2="13"/>
                                <line x1="16" y1="17" x2="8" y2="17"/>
                                <polyline points="10,9 9,9 8,9"/>
                            </svg>
                            تفاصيل الموعد
                        </div>

                        <div class="title-privacy-row">
                            <div class="form-group title-group">
                                <label class="form-label">عنوان الموعد *</label>
                                <input type="text" class="form-input" id="appointmentTitle"
                                       placeholder="مثال: اجتماع العمل، عشاء العائلة"
                                       required maxlength="100">
                                <div class="input-hint">اختر عنواناً واضحاً ومفهوماً</div>
                            </div>

                            <div class="form-group privacy-group">
                                <label class="form-label">الخصوصية *</label>
                                <div class="privacy-options-compact">
                                    <label class="privacy-option-compact selected">
                                        <input type="radio" name="privacy" value="open" checked>
                                        <div class="privacy-content-compact">
                                            <span class="privacy-icon-small">🌍</span>
                                            <span class="privacy-title-small">مفتوح</span>
                                        </div>
                                    </label>
                                    <label class="privacy-option-compact">
                                        <input type="radio" name="privacy" value="private">
                                        <div class="privacy-content-compact">
                                            <span class="privacy-icon-small">🔒</span>
                                            <span class="privacy-title-small">خاص</span>
                                        </div>
                                    </label>
                                </div>
                                <div class="privacy-hint">مفتوح: للجميع | خاص: للمدعوين فقط</div>
                            </div>
                        </div>
                    </div>

                    <!-- Location -->
                    <div class="form-section">
                        <div class="section-title">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                <circle cx="12" cy="10" r="3"/>
                            </svg>
                            الموقع
                        </div>

                        <div class="form-group">
                            <label class="form-label">المنطقة *</label>
                            <input type="text" class="form-input" id="appointmentRegion"
                                   placeholder="مثال: الكويت، حولي، الجهراء"
                                   required maxlength="50">
                        </div>

                        <div class="form-group">
                            <label class="form-label">المبنى أو المكان المحدد</label>
                            <input type="text" class="form-input" id="appointmentBuilding"
                                   placeholder="مثال: برج التجارة، مجمع الأفنيوز (اختياري)"
                                   maxlength="50">
                            <div class="input-hint">يمكنك ترك هذا الحقل فارغاً</div>
                        </div>
                    </div>

                    <!-- Date & Time -->
                    <div class="form-section">
                        <div class="section-title">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                                <line x1="16" y1="2" x2="16" y2="6"/>
                                <line x1="8" y1="2" x2="8" y2="6"/>
                                <line x1="3" y1="10" x2="21" y2="10"/>
                            </svg>
                            التاريخ والوقت
                        </div>

                        <!-- Calendar Type Selection -->
                        <div class="form-group">
                            <label class="form-label">نوع التقويم *</label>
                            <div class="calendar-options">
                                <label class="calendar-option selected" id="gregorianOption">
                                    <input type="radio" name="calendarType" value="gregorian" checked>
                                    <div class="calendar-content">
                                        <span class="calendar-icon">📅</span>
                                        <span class="calendar-title">ميلادي</span>
                                    </div>
                                </label>
                                <label class="calendar-option" id="hijriOption">
                                    <input type="radio" name="calendarType" value="hijri">
                                    <div class="calendar-content">
                                        <span class="calendar-icon">🌙</span>
                                        <span class="calendar-title">هجري</span>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Date Input Section -->
                        <div class="date-input-section">
                            <!-- Day -->
                            <div class="form-group">
                                <label class="form-label">اليوم *</label>
                                <select class="form-input" id="daySelect" required>
                                    <option value="">اختر اليوم</option>
                                </select>
                            </div>

                            <!-- Month -->
                            <div class="form-group">
                                <label class="form-label">الشهر *</label>
                                <select class="form-input" id="monthSelect" required>
                                    <option value="">اختر الشهر</option>
                                </select>
                            </div>

                            <!-- Year -->
                            <div class="form-group">
                                <label class="form-label">السنة *</label>
                                <select class="form-input" id="yearSelect" required>
                                    <option value="">اختر السنة</option>
                                </select>
                            </div>

                            <!-- Time -->
                            <div class="form-group">
                                <label class="form-label">الوقت *</label>
                                <input type="time" class="form-input" id="timeInput" required>
                            </div>
                        </div>

                        <!-- Date Display -->
                        <div class="date-display-section">
                            <!-- Weekday Display -->
                            <div class="weekday-display">
                                <span class="weekday-label">يوم:</span>
                                <span class="weekday-value" id="weekdayDisplay">اختر التاريخ لرؤية اليوم</span>
                            </div>

                            <!-- Conversion Display -->
                            <div class="date-conversion">
                                <span class="conversion-label" id="conversionLabel">التاريخ المقابل:</span>
                                <span class="conversion-value" id="conversionDisplay">اختر التاريخ</span>
                            </div>
                        </div>
                    </div>

                    <!-- Guests -->
                    <div class="form-section">
                        <div class="section-title">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                            </svg>
                            المدعوين
                        </div>

                        <div class="form-group">
                            <label class="form-label">ابحث عن مستخدمين لدعوتهم</label>
                            <div class="search-container">
                                <input type="text" class="form-input search-input" id="guestSearch"
                                       placeholder="ابحث بالاسم أو اسم المستخدم..."
                                       onkeyup="searchUsers(this.value)">
                                <div class="search-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="11" cy="11" r="8"/>
                                        <path d="m21 21-4.35-4.35"/>
                                    </svg>
                                </div>
                            </div>
                            <div id="searchResults" class="search-results"></div>
                        </div>

                        <div class="selected-guests" id="selectedGuests">
                            <div class="guests-header">المدعوين المختارين</div>
                            <div class="guests-list" id="guestsList">
                                <div class="no-guests">لم يتم اختيار مدعوين بعد</div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit -->
                    <div class="form-actions">
                        <button type="button" class="auth-btn auth-btn-secondary" onclick="closeAddAppointment()">
                            إلغاء
                        </button>
                        <button type="submit" class="auth-btn auth-btn-primary" id="createAppointmentBtn">
                            إنشاء الموعد
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Main App -->
        <div class="main-app" id="mainApp">
            <div class="app-bar">
                <span id="appBarTitle">مواعيدي</span>
            </div>

            <div class="content">
                <!-- Home Tab -->
                <div id="home" class="tab-content active">
                    <div id="connectionStatus" class="connection-status">
                        🔗 جاري الاتصال مع Pocketbase...
                    </div>

                    <!-- Appointments Header -->
                <div class="appointments-header">
                    <div class="appointments-filter">
                        <button class="filter-btn active" data-filter="all" onclick="filterAppointments('all')">الكل</button>
                        <button class="filter-btn" data-filter="my" onclick="filterAppointments('my')">مواعيدي</button>
                        <button class="filter-btn" data-filter="invited" onclick="filterAppointments('invited')">مدعو إليها</button>
                    </div>
                    <button class="refresh-btn" onclick="loadAppointments()" title="تحديث المواعيد">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="23,4 23,10 17,10"/>
                            <polyline points="1,20 1,14 7,14"/>
                            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
                        </svg>
                    </button>
                </div>

                <!-- Loading State -->
                <div id="appointmentsLoading" class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>جاري تحميل المواعيد...</p>
                </div>

                <!-- Empty State -->
                <div id="appointmentsEmpty" class="empty-state" style="display: none;">
                    <div class="empty-icon">📅</div>
                    <h3>لا توجد مواعيد</h3>
                    <p>ابدأ بإنشاء موعد جديد أو انتظر دعوة من الأصدقاء</p>
                    <button class="auth-btn auth-btn-primary" onclick="showAddAppointment()">
                        إنشاء موعد جديد
                    </button>
                </div>

                    <!-- Appointments List -->
                    <div id="appointmentsList" class="appointments-list">
                        <!-- Appointments will be loaded here -->
                    </div>
                </div>

                <!-- Settings Tab -->
                <div id="settings" class="tab-content">
                    <div class="settings-container">
                        <h2>⚙️ الإعدادات</h2>

                        <!-- الملف الشخصي -->
                        <div class="settings-section">
                            <h3>👤 الملف الشخصي</h3>
                            <div class="setting-item profile-setting" onclick="openProfileSettings()">
                                <div class="setting-info">
                                    <div class="setting-icon">👤</div>
                                    <div class="setting-details">
                                        <div class="setting-title">إدارة الملف الشخصي</div>
                                        <div class="setting-description">تعديل الاسم، الصورة، والمعلومات الشخصية</div>
                                    </div>
                                </div>
                                <div class="setting-arrow">←</div>
                            </div>
                        </div>

                        <!-- تصحيح التاريخ الهجري -->
                        <div class="settings-section">
                            <h3>🌙 التقويم الهجري</h3>
                            <div class="setting-item hijri-setting" onclick="openHijriCorrection()">
                                <div class="setting-info">
                                    <div class="setting-icon">🌙</div>
                                    <div class="setting-details">
                                        <div class="setting-title">تصحيح التاريخ الهجري</div>
                                        <div class="setting-description">تحديد التاريخ الهجري الفعلي للشهر الحالي</div>
                                    </div>
                                </div>
                                <div class="setting-arrow">←</div>
                            </div>
                        </div>

                        <!-- المظهر والخط -->
                        <div class="settings-section">
                            <h3>🎨 المظهر</h3>
                            <div class="setting-item">
                                <div class="setting-info">
                                    <div class="setting-icon">🌙</div>
                                    <div class="setting-details">
                                        <div class="setting-title">الوضع الداكن</div>
                                        <div class="setting-description">تفعيل المظهر الداكن للتطبيق</div>
                                    </div>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="darkMode" onchange="toggleDarkMode()">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <div class="setting-icon">📝</div>
                                    <div class="setting-details">
                                        <div class="setting-title">حجم الخط</div>
                                        <div class="setting-description">تغيير حجم النص في التطبيق</div>
                                    </div>
                                </div>
                                <select id="fontSize" onchange="changeFontSize()" class="setting-select">
                                    <option value="small">صغير</option>
                                    <option value="medium" selected>متوسط</option>
                                    <option value="large">كبير</option>
                                </select>
                            </div>
                        </div>

                        <!-- الإشعارات -->
                        <div class="settings-section">
                            <h3>🔔 الإشعارات</h3>
                            <div class="setting-item">
                                <div class="setting-info">
                                    <div class="setting-icon">🔔</div>
                                    <div class="setting-details">
                                        <div class="setting-title">تفعيل الإشعارات</div>
                                        <div class="setting-description">استقبال إشعارات المواعيد والأحداث</div>
                                    </div>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="notifications" checked onchange="toggleNotifications()">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <div class="setting-icon">⏰</div>
                                    <div class="setting-details">
                                        <div class="setting-title">تذكير المواعيد</div>
                                        <div class="setting-description">إشعار قبل موعد الحدث</div>
                                    </div>
                                </div>
                                <select id="reminderTime" onchange="setReminderTime()" class="setting-select">
                                    <option value="0">عند الموعد</option>
                                    <option value="15" selected>15 دقيقة قبل</option>
                                    <option value="30">30 دقيقة قبل</option>
                                    <option value="60">ساعة قبل</option>
                                </select>
                            </div>
                        </div>

                        <!-- الخصوصية -->
                        <div class="settings-section">
                            <h3>🔒 الخصوصية</h3>
                            <div class="setting-item">
                                <div class="setting-info">
                                    <div class="setting-icon">👁️</div>
                                    <div class="setting-details">
                                        <div class="setting-title">إظهار الملف الشخصي</div>
                                        <div class="setting-description">من يمكنه رؤية ملفك الشخصي</div>
                                    </div>
                                </div>
                                <select id="profileVisibility" onchange="setProfileVisibility()" class="setting-select">
                                    <option value="public">عام</option>
                                    <option value="friends">الأصدقاء فقط</option>
                                    <option value="private">خاص</option>
                                </select>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <div class="setting-icon">📅</div>
                                    <div class="setting-details">
                                        <div class="setting-title">إظهار المواعيد</div>
                                        <div class="setting-description">من يمكنه رؤية مواعيدك</div>
                                    </div>
                                </div>
                                <select id="appointmentsVisibility" onchange="setAppointmentsVisibility()" class="setting-select">
                                    <option value="public">عام</option>
                                    <option value="friends" selected>الأصدقاء فقط</option>
                                    <option value="private">خاص</option>
                                </select>
                            </div>
                        </div>

                        <!-- عام -->
                        <div class="settings-section">
                            <h3>📱 عام</h3>
                            <div class="setting-item setting-button" onclick="exportData()">
                                <div class="setting-info">
                                    <div class="setting-icon">📤</div>
                                    <div class="setting-details">
                                        <div class="setting-title">تصدير البيانات</div>
                                        <div class="setting-description">تحميل نسخة من بياناتك</div>
                                    </div>
                                </div>
                                <div class="setting-arrow">←</div>
                            </div>

                            <div class="setting-item setting-button" onclick="showAbout()">
                                <div class="setting-info">
                                    <div class="setting-icon">ℹ️</div>
                                    <div class="setting-details">
                                        <div class="setting-title">حول التطبيق</div>
                                        <div class="setting-description">معلومات عن سجلي والإصدار</div>
                                    </div>
                                </div>
                                <div class="setting-arrow">←</div>
                            </div>

                            <div class="setting-item setting-button logout-setting" onclick="logout()">
                                <div class="setting-info">
                                    <div class="setting-icon">🚪</div>
                                    <div class="setting-details">
                                        <div class="setting-title">تسجيل الخروج</div>
                                        <div class="setting-description">الخروج من حسابك</div>
                                    </div>
                                </div>
                                <div class="setting-arrow">←</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Tab -->
                <div id="profile" class="tab-content">
                    <div class="profile-container">
                        <h2>👤 الملف الشخصي</h2>

                        <!-- Profile Header -->
                        <div class="profile-header">
                            <div class="profile-avatar">
                                <img id="profileAvatar" src="https://via.placeholder.com/100x100/4CAF50/white?text=👤" alt="الصورة الشخصية">
                                <button class="avatar-edit-btn" onclick="changeAvatar()">📷</button>
                            </div>
                            <div class="profile-info">
                                <h3 id="profileName">اسم المستخدم</h3>
                                <p id="profileUsername">@username</p>
                                <p id="profileEmail"><EMAIL></p>
                            </div>
                        </div>

                        <!-- Profile Stats -->
                        <div class="profile-stats">
                            <div class="stat-item">
                                <div class="stat-number" id="appointmentsCount">0</div>
                                <div class="stat-label">المواعيد</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="friendsCount">0</div>
                                <div class="stat-label">الأصدقاء</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="invitationsCount">0</div>
                                <div class="stat-label">الدعوات</div>
                            </div>
                        </div>

                        <!-- Profile Sections -->
                        <div class="profile-sections">
                            <!-- Personal Information -->
                            <div class="profile-section">
                                <h3>📝 المعلومات الشخصية</h3>
                                <div class="profile-field">
                                    <label>الاسم الكامل</label>
                                    <input type="text" id="fullName" placeholder="أدخل اسمك الكامل">
                                </div>
                                <div class="profile-field">
                                    <label>اسم المستخدم</label>
                                    <input type="text" id="username" placeholder="أدخل اسم المستخدم">
                                </div>
                                <div class="profile-field">
                                    <label>البريد الإلكتروني</label>
                                    <input type="email" id="email" placeholder="أدخل بريدك الإلكتروني">
                                </div>
                                <div class="profile-field">
                                    <label>نبذة شخصية</label>
                                    <textarea id="bio" placeholder="اكتب نبذة عن نفسك" rows="3"></textarea>
                                </div>
                            </div>

                            <!-- Location Information -->
                            <div class="profile-section">
                                <h3>📍 معلومات الموقع</h3>
                                <div class="profile-field">
                                    <label>المدينة</label>
                                    <input type="text" id="city" placeholder="أدخل مدينتك">
                                </div>
                                <div class="profile-field">
                                    <label>الدولة</label>
                                    <input type="text" id="country" placeholder="أدخل دولتك">
                                </div>
                            </div>

                            <!-- Preferences -->
                            <div class="profile-section">
                                <h3>⚙️ التفضيلات</h3>
                                <div class="profile-field">
                                    <label>التقويم المفضل</label>
                                    <select id="preferredCalendar">
                                        <option value="hijri">الهجري</option>
                                        <option value="gregorian">الميلادي</option>
                                    </select>
                                </div>
                                <div class="profile-field">
                                    <label>اللغة</label>
                                    <select id="language">
                                        <option value="ar">العربية</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="profile-actions">
                                <button onclick="saveProfile()" class="save-btn">💾 حفظ التغييرات</button>
                                <button onclick="resetProfile()" class="reset-btn">🔄 إعادة تعيين</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Floating Action Button -->
            <button class="fab" id="fabBtn">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="5" x2="12" y2="19"/>
                    <line x1="5" y1="12" x2="19" y2="12"/>
                </svg>
            </button>

            <!-- Bottom Navigation -->
            <div class="bottom-nav">
                <div class="nav-item active" id="homeTab">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                            <polyline points="9,22 9,12 15,12 15,22"/>
                        </svg>
                    </div>
                    <div>الرئيسية</div>
                </div>
                <div class="nav-item" id="searchTab">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="m21 21-4.35-4.35"/>
                        </svg>
                    </div>
                    <div>البحث</div>
                </div>
                <div class="nav-item" id="addTab">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"/>
                            <line x1="5" y1="12" x2="19" y2="12"/>
                        </svg>
                    </div>
                    <div>إضافة</div>
                </div>
                <div class="nav-item" id="notificationsTab">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"/>
                            <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"/>
                        </svg>
                    </div>
                    <div>الإشعارات</div>
                </div>
                <div class="nav-item" id="settingsTab">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                        </svg>
                    </div>
                    <div>الإعدادات</div>
                </div>
            </div>
        </div>
    </div>



    <!-- Hijri Correction Modal -->
    <div id="hijriCorrectionModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🌙 تصحيح التاريخ الهجري</h3>
                <button onclick="closeHijriCorrection()" class="close-btn">×</button>
            </div>

            <div class="hijri-correction-panel">
                <p>حدد التاريخ الهجري الصحيح لليوم:</p>

                <div class="date-selection">
                    <h4>التاريخ الحالي حسب الحساب الفلكي: <span id="calculated-hijri-date">7 صفر 1447</span></h4>

                    <div class="actual-date-options">
                        <label>حدد التاريخ الفعلي لليوم:</label>
                        <div class="date-radio-group" id="hijri-date-options">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>

                <div class="correction-actions">
                    <button onclick="applyDateCorrection()" class="apply-btn">
                        ✅ تطبيق التاريخ المحدد
                    </button>
                    <button onclick="resetToCalculated()" class="reset-btn">
                        🔄 العودة للحساب الفلكي
                    </button>
                </div>

                <div class="correction-preview">
                    <h4>تأثير التصحيح على المواعيد:</h4>
                    <div id="correction-preview-content">
                        <!-- عرض كيف ستتأثر المواعيد -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer"></div>

    <script src="https://cdn.jsdelivr.net/npm/pocketbase@0.21.3/dist/pocketbase.umd.js"></script>
    <script>
        // Hijri Calendar Conversion Functions
        function isLeapYear(year) {
            return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
        }

        function getDaysInMonth(month, year) {
            const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
            if (month === 2 && isLeapYear(year)) {
                return 29;
            }
            return daysInMonth[month - 1];
        }

        function gregorianToJulianDay(year, month, day) {
            // Standard Gregorian to Julian Day conversion
            let a = Math.floor((14 - month) / 12);
            let y = year + 4800 - a;
            let m = month + 12 * a - 3;

            let jd = day + Math.floor((153 * m + 2) / 5) + 365 * y + Math.floor(y / 4) - Math.floor(y / 100) + Math.floor(y / 400) - 32045;

            return jd;
        }

        function julianDayToGregorian(jd) {
            // Standard Julian Day to Gregorian conversion
            let a = jd + 32044;
            let b = Math.floor((4 * a + 3) / 146097);
            let c = a - Math.floor((146097 * b) / 4);
            let d = Math.floor((4 * c + 3) / 1461);
            let e = c - Math.floor((1461 * d) / 4);
            let m = Math.floor((5 * e + 2) / 153);

            let day = e - Math.floor((153 * m + 2) / 5) + 1;
            let month = m + 3 - 12 * Math.floor(m / 10);
            let year = 100 * b + d - 4800 + Math.floor(m / 10);

            return { year: Math.floor(year), month: Math.floor(month), day: Math.floor(day) };
        }

        function hijriToJulianDay(year, month, day) {
            // Kuwaiti algorithm for Hijri to Julian Day conversion
            // More accurate conversion based on astronomical calculations

            // Calculate total days from Hijri epoch
            let totalDays = 0;

            // Add days for complete years
            for (let y = 1; y < year; y++) {
                totalDays += isHijriLeapYear(y) ? 355 : 354;
            }

            // Add days for complete months in current year
            for (let m = 1; m < month; m++) {
                totalDays += getHijriMonthLength(m, year);
            }

            // Add remaining days
            totalDays += day - 1;

            // Hijri epoch: July 16, 622 CE (Julian Day 1948439)
            return totalDays + 1948439;
        }

        function julianDayToHijri(jd) {
            // Convert Julian Day to Hijri using iterative method
            const daysSinceEpoch = jd - 1948439;

            // Estimate year
            let year = Math.floor(daysSinceEpoch / 354.36667) + 1;
            let remainingDays = daysSinceEpoch;

            // Adjust year by counting actual days
            year = 1;
            while (remainingDays >= (isHijriLeapYear(year) ? 355 : 354)) {
                remainingDays -= isHijriLeapYear(year) ? 355 : 354;
                year++;
            }

            // Find month
            let month = 1;
            while (remainingDays >= getHijriMonthLength(month, year)) {
                remainingDays -= getHijriMonthLength(month, year);
                month++;
            }

            // Remaining days is the day of month
            const day = remainingDays + 1;

            return { year: Math.floor(year), month: Math.floor(month), day: Math.floor(day) };
        }

        function isHijriLeapYear(year) {
            // Hijri leap year cycle: 11 leap years in every 30-year cycle
            const cycle = year % 30;
            const leapYears = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
            return leapYears.includes(cycle);
        }

        function getHijriMonthLength(month, year) {
            // Standard Hijri month lengths
            if (month % 2 === 1) {
                return 30; // Odd months have 30 days
            } else if (month === 12) {
                return isHijriLeapYear(year) ? 30 : 29; // Dhul Hijjah
            } else {
                return 29; // Even months (except 12) have 29 days
            }
        }

        function gregorianToHijri(gYear, gMonth, gDay) {
            console.log(`Converting Gregorian ${gDay}/${gMonth}/${gYear} to Hijri`);
            const jd = gregorianToJulianDay(gYear, gMonth, gDay);
            console.log(`Julian Day: ${jd}`);
            const hijriDate = julianDayToHijri(jd);
            console.log(`Hijri result: ${hijriDate.day}/${hijriDate.month}/${hijriDate.year}`);
            return hijriDate;
        }

        function hijriToGregorian(hYear, hMonth, hDay) {
            console.log(`Converting Hijri ${hDay}/${hMonth}/${hYear} to Gregorian`);
            const jd = hijriToJulianDay(hYear, hMonth, hDay);
            console.log(`Julian Day: ${jd}`);
            const gregorianDate = julianDayToGregorian(jd);
            console.log(`Gregorian result: ${gregorianDate.day}/${gregorianDate.month}/${gregorianDate.year}`);
            return gregorianDate;
        }

        function getHijriMonthName(month) {
            const months = [
                'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني',
                'جمادى الأولى', 'جمادى الثانية', 'رجب', 'شعبان',
                'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
            ];
            return months[month - 1] || '';
        }

        function getGregorianMonthName(month) {
            const months = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];
            return months[month - 1] || '';
        }

        function getWeekdayName(date) {
            const weekdays = [
                'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء',
                'الخميس', 'الجمعة', 'السبت'
            ];
            return weekdays[date.getDay()];
        }

        function getDaysInGregorianMonth(month, year) {
            // month is 1-based (1 = January, 2 = February, etc.)
            // new Date(year, month, 0) gives last day of previous month
            // so we use new Date(year, month, 0) where month is the actual month number
            return new Date(year, month, 0).getDate();
        }

        function getDaysInHijriMonth(month, year) {
            // Use the same logic as the conversion functions
            return getHijriMonthLength(month, year);
        }
    </script>
    <script>
        console.log('Sijilli App Starting...');

        // Global variables
        let pb = null;
        let currentUser = null;
        const POCKETBASE_URL = 'http://127.0.0.1:8090';

        // Initialize app when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing app...');
            initializeApp();
        });

        // Initialize the application
        async function initializeApp() {
            try {
                console.log('Initializing PocketBase...');
                pb = new PocketBase(POCKETBASE_URL);
                console.log('PocketBase initialized successfully');

                await checkConnection();
                setupEventListeners();

            } catch (error) {
                console.error('Failed to initialize app:', error);
                showConnectionStatus('خطأ في تهيئة التطبيق: ' + error.message, 'error');
            }
        }

        // Check connection to PocketBase
        async function checkConnection() {
            try {
                console.log('Checking PocketBase connection...');
                await pb.health.check();
                console.log('Connection successful');
                showConnectionStatus('✅ متصل مع Pocketbase بنجاح', 'success');

                // Check if user is already authenticated
                if (pb.authStore.isValid) {
                    console.log('User already authenticated');
                    currentUser = pb.authStore.model;
                    showMainApp();
                }
            } catch (error) {
                console.error('Connection failed:', error);
                showConnectionStatus('❌ فشل الاتصال مع Pocketbase - تأكد من تشغيل الخادم', 'error');
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            console.log('Setting up event listeners...');

            // Login button
            const loginBtn = document.getElementById('loginBtn');
            if (loginBtn) {
                loginBtn.addEventListener('click', handleLogin);
                console.log('Login button listener added');
            }

            // Register link
            const registerLink = document.getElementById('registerLink');
            if (registerLink) {
                registerLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    showRegisterScreen();
                });
            }

            // Back to login link
            const backToLoginLink = document.getElementById('backToLoginLink');
            if (backToLoginLink) {
                backToLoginLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    showLoginScreen();
                });
            }

            // Register form buttons
            const nextStepBtn = document.getElementById('nextStepBtn');
            if (nextStepBtn) {
                nextStepBtn.addEventListener('click', nextStep);
            }

            const prevStepBtn = document.getElementById('prevStepBtn');
            if (prevStepBtn) {
                prevStepBtn.addEventListener('click', prevStep);
            }

            const registerBtn = document.getElementById('registerBtn');
            if (registerBtn) {
                registerBtn.addEventListener('click', handleRegister);
            }

            // Enter key in login form
            const emailInput = document.getElementById('emailInput');
            const passwordInput = document.getElementById('passwordInput');

            if (emailInput) {
                emailInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') handleLogin();
                });
            }

            if (passwordInput) {
                passwordInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') handleLogin();
                });
            }

            // FAB button
            const fabBtn = document.getElementById('fabBtn');
            if (fabBtn) {
                fabBtn.addEventListener('click', function() {
                    showAddAppointment();
                });
            }

            // Navigation tabs
            setupNavigation();

            console.log('Event listeners setup complete');
        }

        // Handle login
        async function handleLogin() {
            console.log('Login attempt started');

            const emailInput = document.getElementById('emailInput');
            const passwordInput = document.getElementById('passwordInput');
            const loginBtn = document.getElementById('loginBtn');
            const errorEl = document.getElementById('errorMessage');

            const email = emailInput.value.trim();
            const password = passwordInput.value;

            console.log('Login data:', { email, password: password ? '***' : 'empty' });

            // Validation
            if (!email || !password) {
                showError('يرجى إدخال البريد الإلكتروني وكلمة المرور');
                return;
            }

            if (!pb) {
                showError('لم يتم الاتصال مع Pocketbase بعد');
                return;
            }

            // Update UI
            loginBtn.disabled = true;
            loginBtn.textContent = 'جاري تسجيل الدخول...';
            errorEl.style.display = 'none';

            try {
                console.log('Attempting authentication...');
                const authData = await pb.collection('users').authWithPassword(email, password);
                console.log('Authentication successful:', authData);

                currentUser = authData.record;
                showMainApp();
                showNotification('تم تسجيل الدخول بنجاح!', 'success');

            } catch (error) {
                console.error('Login failed:', error);

                let errorMessage = 'خطأ غير معروف';
                if (error.status === 400) {
                    errorMessage = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
                } else if (error.message) {
                    errorMessage = error.message;
                }

                showError('فشل تسجيل الدخول: ' + errorMessage);

            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = 'تسجيل الدخول';
            }
        }

        // Show main app
        function showMainApp() {
            console.log('Showing main app');
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('registerScreen').style.display = 'none';
            document.getElementById('mainApp').style.display = 'block';

            // Show home tab by default
            showTab('home');

            // Load user data
            if (currentUser) {
                document.getElementById('appBarTitle').textContent = 'مرحباً ' + currentUser.username;
                // Load appointments
                loadAppointments();
            }
        }

        // Show register screen
        function showRegisterScreen() {
            console.log('Showing register screen');
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('registerScreen').style.display = 'flex';
            document.getElementById('mainApp').style.display = 'none';
            resetRegisterForm();
        }

        // Show login screen
        function showLoginScreen() {
            console.log('Showing login screen');
            document.getElementById('registerScreen').style.display = 'none';
            document.getElementById('loginScreen').style.display = 'flex';
            document.getElementById('mainApp').style.display = 'none';
        }

        // Reset register form
        function resetRegisterForm() {
            // Clear all inputs
            document.getElementById('registerUsername').value = '';
            document.getElementById('registerEmail').value = '';
            document.getElementById('registerPassword').value = '';
            document.getElementById('registerPasswordConfirm').value = '';
            document.getElementById('registerDisplayName').value = '';
            document.getElementById('registerJob').value = '';
            document.getElementById('registerPhone').value = '';

            // Reset to step 1
            document.getElementById('registerStep1').style.display = 'block';
            document.getElementById('registerStep2').style.display = 'none';

            // Clear error messages
            const errorEl = document.getElementById('registerErrorMessage');
            if (errorEl) errorEl.style.display = 'none';
        }

        // Register step navigation
        function nextStep() {
            console.log('Moving to next step');

            const username = document.getElementById('registerUsername').value.trim();
            const email = document.getElementById('registerEmail').value.trim();
            const password = document.getElementById('registerPassword').value;
            const passwordConfirm = document.getElementById('registerPasswordConfirm').value;

            try {
                // Validation
                if (!username) {
                    throw new Error('اسم المستخدم مطلوب');
                }

                if (!isValidUsername(username)) {
                    throw new Error('اسم المستخدم غير صحيح. يجب أن يحتوي على حروف إنجليزية وأرقام ونقطة وأندرسكور فقط');
                }

                if (!email) {
                    throw new Error('البريد الإلكتروني مطلوب');
                }

                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    throw new Error('البريد الإلكتروني غير صحيح');
                }

                if (!password) {
                    throw new Error('كلمة المرور مطلوبة');
                }

                if (password.length < 8) {
                    throw new Error('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                }

                if (password !== passwordConfirm) {
                    throw new Error('كلمة المرور وتأكيدها غير متطابقتين');
                }

                // Move to step 2
                document.getElementById('registerStep1').style.display = 'none';
                document.getElementById('registerStep2').style.display = 'block';
                hideRegisterError();

            } catch (error) {
                showRegisterError(error.message);
            }
        }

        function prevStep() {
            console.log('Moving to previous step');
            document.getElementById('registerStep2').style.display = 'none';
            document.getElementById('registerStep1').style.display = 'block';
            hideRegisterError();
        }

        // Username validation
        function isValidUsername(username) {
            const regex = /^[a-zA-Z0-9._]+$/;
            if (!regex.test(username)) return false;
            if (username.startsWith('.') || username.startsWith('_')) return false;
            if (username.endsWith('.') || username.endsWith('_')) return false;
            if (username.includes('..') || username.includes('__') || username.includes('._') || username.includes('_.')) return false;
            return true;
        }

        // Handle registration
        async function handleRegister() {
            console.log('Registration attempt started');

            const registerBtn = document.getElementById('registerBtn');
            const originalText = registerBtn.textContent;

            try {
                registerBtn.disabled = true;
                registerBtn.textContent = 'جاري إنشاء الحساب...';

                // Get form data
                const username = document.getElementById('registerUsername').value.trim();
                const email = document.getElementById('registerEmail').value.trim();
                const password = document.getElementById('registerPassword').value;
                const displayName = document.getElementById('registerDisplayName').value.trim();
                const job = document.getElementById('registerJob').value.trim();
                const phone = document.getElementById('registerPhone').value.trim();

                console.log('Registration data:', { username, email, displayName, job, phone });

                // Check if username is available
                try {
                    const existingUsers = await pb.collection('users').getList(1, 1, {
                        filter: `username = "${username}"`,
                    });

                    if (existingUsers.items.length > 0) {
                        throw new Error('اسم المستخدم مستخدم بالفعل');
                    }
                } catch (checkError) {
                    if (checkError.message === 'اسم المستخدم مستخدم بالفعل') {
                        throw checkError;
                    }
                    console.warn('Could not check username availability:', checkError);
                }

                // Check if email is available
                try {
                    const existingEmails = await pb.collection('users').getList(1, 1, {
                        filter: `email = "${email}"`,
                    });

                    if (existingEmails.items.length > 0) {
                        throw new Error('البريد الإلكتروني مستخدم بالفعل');
                    }
                } catch (checkError) {
                    if (checkError.message === 'البريد الإلكتروني مستخدم بالفعل') {
                        throw checkError;
                    }
                    console.warn('Could not check email availability:', checkError);
                }

                // Create user account
                const userData = {
                    username: username,
                    email: email,
                    password: password,
                    passwordConfirm: password,
                    role: 'user',
                    isPublic: true,
                    verified: false
                };

                console.log('Creating user...');
                const user = await pb.collection('users').create(userData);
                console.log('User created successfully:', user);

                // Create profile if any profile data provided
                if (displayName || job || phone) {
                    try {
                        const profileData = {
                            user: user.id,
                            displayName: displayName || '',
                            job: job || '',
                            phone: phone || '',
                            bio: '',
                            instagram: '',
                            youtube: ''
                        };

                        console.log('Creating profile...');
                        const profile = await pb.collection('profiles').create(profileData);
                        console.log('Profile created successfully:', profile);
                    } catch (profileError) {
                        console.warn('Could not create profile, but user was created:', profileError);
                    }
                }

                // Auto-login the new user
                try {
                    await pb.collection('users').authWithPassword(email, password);
                    currentUser = pb.authStore.model;
                    console.log('Auto-login successful:', currentUser);

                    showNotification('تم إنشاء الحساب بنجاح! مرحباً بك في سجلي', 'success');
                    showMainApp();

                } catch (loginError) {
                    console.warn('Auto-login failed, but account was created:', loginError);
                    showNotification('تم إنشاء الحساب بنجاح! يرجى تسجيل الدخول', 'success');
                    showLoginScreen();
                }

            } catch (error) {
                console.error('Registration failed:', error);
                showRegisterError('خطأ في إنشاء الحساب: ' + error.message);
            } finally {
                registerBtn.disabled = false;
                registerBtn.textContent = originalText;
            }
        }

        // Show/hide register errors
        function showRegisterError(message) {
            const errorEl = document.getElementById('registerErrorMessage');
            if (errorEl) {
                errorEl.textContent = message;
                errorEl.style.display = 'block';
            }
        }

        function hideRegisterError() {
            const errorEl = document.getElementById('registerErrorMessage');
            if (errorEl) {
                errorEl.style.display = 'none';
            }
        }

        // Setup navigation
        function setupNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    // Remove active class from all items
                    navItems.forEach(nav => nav.classList.remove('active'));
                    // Add active class to clicked item
                    this.classList.add('active');

                    // Handle navigation
                    const tabId = this.id;
                    console.log('Navigation clicked:', tabId);

                    switch(tabId) {
                        case 'homeTab':
                            showTab('home');
                            loadAppointments();
                            break;
                        case 'searchTab':
                            showNotification('البحث قريباً...', 'info');
                            break;
                        case 'addTab':
                            showAddAppointment();
                            break;
                        case 'notificationsTab':
                            showNotification('الإشعارات قريباً...', 'info');
                            break;
                        case 'settingsTab':
                            showTab('settings');
                            break;
                    }
                });
            });
        }

        // Show error message
        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
        }

        // Show connection status
        function showConnectionStatus(message, type = 'info') {
            const statusEl = document.getElementById('connectionStatus');
            if (statusEl) {
                statusEl.textContent = message;
                statusEl.className = `connection-status ${type}`;
            }
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            container.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Hide and remove notification
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    container.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Tab Management
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const allTabs = document.querySelectorAll('.tab-content');
            allTabs.forEach(tab => {
                tab.style.display = 'none';
            });

            // إظهار التبويب المطلوب
            const targetTab = document.getElementById(tabName);
            if (targetTab) {
                targetTab.style.display = 'block';

                // تحديث محتوى التبويب حسب النوع
                if (tabName === 'profile') {
                    loadProfile();
                } else if (tabName === 'settings') {
                    loadSettings();
                }
            }

            // إظهار التطبيق الرئيسي
            document.getElementById('mainApp').style.display = 'block';
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('profileScreen').style.display = 'none';
        }

        function loadSettings() {
            // تحميل الإعدادات المحفوظة
            const darkMode = localStorage.getItem('darkMode') === 'true';
            const fontSize = localStorage.getItem('fontSize') || 'medium';
            const notifications = localStorage.getItem('notifications') !== 'false';
            const profileVisibility = localStorage.getItem('profileVisibility') || 'friends';
            const appointmentsVisibility = localStorage.getItem('appointmentsVisibility') || 'friends';
            const reminderTime = localStorage.getItem('reminderTime') || '15';

            // تطبيق الإعدادات على الواجهة
            document.getElementById('darkMode').checked = darkMode;
            document.getElementById('fontSize').value = fontSize;
            document.getElementById('notifications').checked = notifications;
            document.getElementById('profileVisibility').value = profileVisibility;
            document.getElementById('appointmentsVisibility').value = appointmentsVisibility;
            document.getElementById('reminderTime').value = reminderTime;
        }

        // Settings Functions
        function openProfileSettings() {
            showTab('profile');
        }

        function openHijriCorrection() {
            document.getElementById('hijriCorrectionModal').style.display = 'flex';
            loadHijriCorrectionOptions();
        }

        function closeHijriCorrection() {
            document.getElementById('hijriCorrectionModal').style.display = 'none';
        }

        function loadHijriCorrectionOptions() {
            const currentHijriDate = getCurrentHijriDate();
            const calculatedDateSpan = document.getElementById('calculated-hijri-date');
            const optionsContainer = document.getElementById('hijri-date-options');

            const monthName = getHijriMonthName(currentHijriDate.month);
            calculatedDateSpan.textContent = `${currentHijriDate.day} ${monthName} ${currentHijriDate.year}`;

            // إنشاء 5 خيارات: -2, -1, 0, +1, +2
            let optionsHTML = '';
            for (let i = -2; i <= 2; i++) {
                const adjustedDay = currentHijriDate.day + i;
                const isSelected = i === 0; // الافتراضي هو الحساب الفلكي
                const description = i === 0 ? 'حسب الحساب' : `${i > 0 ? '+' : ''}${i} يوم`;

                optionsHTML += `
                    <label class="date-option ${isSelected ? 'selected' : ''}">
                        <input type="radio" name="actual-date" value="${i}" ${isSelected ? 'checked' : ''}>
                        <span class="date-text">${adjustedDay} ${monthName} ${currentHijriDate.year}</span>
                        <span class="correction-indicator">(${description})</span>
                    </label>
                `;
            }

            optionsContainer.innerHTML = optionsHTML;

            // إضافة مستمعي الأحداث
            document.querySelectorAll('input[name="actual-date"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    // تحديث التحديد البصري
                    document.querySelectorAll('.date-option').forEach(option => {
                        option.classList.remove('selected');
                    });
                    this.closest('.date-option').classList.add('selected');

                    // معاينة التأثير
                    previewDateCorrectionEffect();
                });
            });

            // معاينة أولية
            previewDateCorrectionEffect();
        }

        function previewDateCorrectionEffect() {
            const selectedValue = document.querySelector('input[name="actual-date"]:checked')?.value;
            if (!selectedValue) return;

            const correctionValue = parseInt(selectedValue);
            const previewDiv = document.getElementById('correction-preview-content');

            if (correctionValue === 0) {
                previewDiv.innerHTML = '<p>✅ لا يوجد تصحيح - المواعيد تبقى كما هي</p>';
                return;
            }

            // جلب المواعيد الهجرية في الشهر الحالي
            const currentHijriDate = getCurrentHijriDate();
            const currentMonthAppointments = allAppointments.filter(apt =>
                apt.primary_date &&
                apt.primary_date.type === 'hijri' &&
                apt.primary_date.month === currentHijriDate.month &&
                apt.primary_date.year === currentHijriDate.year
            );

            if (currentMonthAppointments.length === 0) {
                previewDiv.innerHTML = '<p>لا توجد مواعيد هجرية في الشهر الحالي</p>';
                return;
            }

            let previewHTML = '<div class="preview-appointments">';
            previewHTML += `<p><strong>التصحيح المطبق: ${correctionValue > 0 ? '+' : ''}${correctionValue} يوم</strong></p>`;

            currentMonthAppointments.forEach(apt => {
                const monthName = getHijriMonthName(apt.primary_date.month);

                previewHTML += `
                    <div class="preview-item">
                        <strong>${apt.title}</strong><br>
                        <span style="color: #666;">التاريخ الأساسي (ثابت): ${apt.primary_date.day} ${monthName}</span><br>
                        <span style="color: #4CAF50;">التاريخ المقابل سيتغير بـ ${correctionValue > 0 ? '+' : ''}${correctionValue} يوم</span>
                    </div>
                `;
            });

            previewHTML += '</div>';
            previewDiv.innerHTML = previewHTML;
        }

        function applyDateCorrection() {
            const selectedValue = document.querySelector('input[name="actual-date"]:checked')?.value;
            if (!selectedValue) return;

            const correctionValue = parseInt(selectedValue);
            const currentHijriDate = getCurrentHijriDate();

            const actualDate = {
                year: currentHijriDate.year,
                month: currentHijriDate.month,
                day: currentHijriDate.day + correctionValue,
                correctionApplied: correctionValue
            };

            // حفظ التصحيح
            const correction = {
                currentMonth: {
                    year: actualDate.year,
                    month: actualDate.month
                },
                correctionValue: correctionValue,
                actualDate: actualDate,
                appliedDate: new Date().toISOString(),
                method: "direct_date_selection"
            };

            localStorage.setItem('hijriCorrection', JSON.stringify(correction));

            // تحديث جميع المواعيد
            updateAllAppointmentsWithCorrection(correctionValue);

            // إشعار المستخدم
            const monthName = getHijriMonthName(actualDate.month);
            showNotification(`✅ تم تحديد التاريخ الفعلي: ${actualDate.day} ${monthName} ${actualDate.year}${correctionValue !== 0 ? ` (تصحيح: ${correctionValue > 0 ? '+' : ''}${correctionValue} يوم)` : ''}`);

            console.log(`📅 التاريخ الفعلي المحدد: ${actualDate.day} ${monthName} ${actualDate.year}`);
            console.log(`🔧 قيمة التصحيح: ${correctionValue}`);

            closeHijriCorrection();
        }

        function resetToCalculated() {
            localStorage.removeItem('hijriCorrection');
            showNotification('تم إعادة تعيين التصحيح للحساب الفلكي');
            closeHijriCorrection();
        }

        function updateAllAppointmentsWithCorrection(correctionValue) {
            // This function will be implemented when we have the appointment correction logic
            console.log('Updating appointments with correction:', correctionValue);
            // For now, just reload appointments
            loadAppointments();
        }

        function toggleDarkMode() {
            const isDark = document.getElementById('darkMode').checked;
            document.body.classList.toggle('dark-mode', isDark);
            localStorage.setItem('darkMode', isDark);
            showNotification(isDark ? 'تم تفعيل الوضع الداكن' : 'تم إلغاء الوضع الداكن');
        }

        function changeFontSize() {
            const fontSize = document.getElementById('fontSize').value;
            document.body.className = document.body.className.replace(/font-\w+/g, '');
            document.body.classList.add(`font-${fontSize}`);
            localStorage.setItem('fontSize', fontSize);
            showNotification('تم تغيير حجم الخط');
        }

        function toggleNotifications() {
            const enabled = document.getElementById('notifications').checked;
            localStorage.setItem('notifications', enabled);
            showNotification(enabled ? 'تم تفعيل الإشعارات' : 'تم إلغاء الإشعارات');
        }

        function setReminderTime() {
            const time = document.getElementById('reminderTime').value;
            localStorage.setItem('reminderTime', time);
            const timeText = time === '0' ? 'عند الموعد' :
                           time === '15' ? '15 دقيقة قبل' :
                           time === '30' ? '30 دقيقة قبل' : 'ساعة قبل';
            showNotification(`تم تعيين التذكير: ${timeText}`);
        }

        function setProfileVisibility() {
            const visibility = document.getElementById('profileVisibility').value;
            localStorage.setItem('profileVisibility', visibility);
            const visibilityText = visibility === 'public' ? 'عام' :
                                 visibility === 'friends' ? 'للأصدقاء فقط' : 'خاص';
            showNotification(`تم تعيين رؤية الملف الشخصي: ${visibilityText}`);
        }

        function setAppointmentsVisibility() {
            const visibility = document.getElementById('appointmentsVisibility').value;
            localStorage.setItem('appointmentsVisibility', visibility);
            const visibilityText = visibility === 'public' ? 'عام' :
                                 visibility === 'friends' ? 'للأصدقاء فقط' : 'خاص';
            showNotification(`تم تعيين رؤية المواعيد: ${visibilityText}`);
        }

        function exportData() {
            const userData = {
                profile: currentUser,
                appointments: allAppointments,
                settings: {
                    darkMode: localStorage.getItem('darkMode'),
                    fontSize: localStorage.getItem('fontSize'),
                    notifications: localStorage.getItem('notifications'),
                    profileVisibility: localStorage.getItem('profileVisibility'),
                    appointmentsVisibility: localStorage.getItem('appointmentsVisibility')
                },
                exportDate: new Date().toISOString()
            };

            const dataStr = JSON.stringify(userData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `sijilli-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            URL.revokeObjectURL(url);
            showNotification('تم تصدير البيانات بنجاح');
        }

        function showAbout() {
            alert(`سجلي - شبكة اجتماعية إسلامية
الإصدار: 1.0.0
تطوير: فريق سجلي
التاريخ: ${new Date().getFullYear()}

ميزات التطبيق:
• إدارة المواعيد بالتقويم الهجري والميلادي
• تصحيح التواريخ الهجرية
• شبكة اجتماعية إسلامية
• دعوة الأصدقاء للمواعيد
• واجهة عربية كاملة`);
        }

        // Profile Functions
        function loadProfile() {
            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            // تحديث معلومات الملف الشخصي
            document.getElementById('profileName').textContent = currentUser.name || 'اسم المستخدم';
            document.getElementById('profileUsername').textContent = '@' + (currentUser.username || 'username');
            document.getElementById('profileEmail').textContent = currentUser.email || '<EMAIL>';

            // تحديث الحقول
            document.getElementById('fullName').value = currentUser.name || '';
            document.getElementById('username').value = currentUser.username || '';
            document.getElementById('email').value = currentUser.email || '';
            document.getElementById('bio').value = currentUser.bio || '';
            document.getElementById('city').value = currentUser.city || '';
            document.getElementById('country').value = currentUser.country || '';
            document.getElementById('preferredCalendar').value = currentUser.preferredCalendar || 'hijri';
            document.getElementById('language').value = currentUser.language || 'ar';

            // تحديث الإحصائيات
            updateProfileStats();
        }

        function updateProfileStats() {
            const appointmentsCount = allAppointments.length;
            const friendsCount = 0; // سيتم تطبيقه لاحقاً
            const invitationsCount = 0; // سيتم تطبيقه لاحقاً

            document.getElementById('appointmentsCount').textContent = appointmentsCount;
            document.getElementById('friendsCount').textContent = friendsCount;
            document.getElementById('invitationsCount').textContent = invitationsCount;
        }

        function changeAvatar() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('profileAvatar').src = e.target.result;
                        showNotification('تم تغيير الصورة الشخصية');
                    };
                    reader.readAsDataURL(file);
                }
            };
            input.click();
        }

        function saveProfile() {
            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            // جمع البيانات من النموذج
            const profileData = {
                name: document.getElementById('fullName').value,
                username: document.getElementById('username').value,
                email: document.getElementById('email').value,
                bio: document.getElementById('bio').value,
                city: document.getElementById('city').value,
                country: document.getElementById('country').value,
                preferredCalendar: document.getElementById('preferredCalendar').value,
                language: document.getElementById('language').value
            };

            // التحقق من صحة البيانات
            if (!profileData.name.trim()) {
                showNotification('يرجى إدخال الاسم الكامل', 'error');
                return;
            }

            if (!profileData.username.trim()) {
                showNotification('يرجى إدخال اسم المستخدم', 'error');
                return;
            }

            if (!profileData.email.trim()) {
                showNotification('يرجى إدخال البريد الإلكتروني', 'error');
                return;
            }

            try {
                // تحديث البيانات المحلية
                Object.assign(currentUser, profileData);

                // حفظ في localStorage
                localStorage.setItem('currentUser', JSON.stringify(currentUser));

                // تحديث الواجهة
                loadProfile();

                showNotification('تم حفظ الملف الشخصي بنجاح', 'success');

                console.log('Profile saved:', profileData);

            } catch (error) {
                console.error('Error saving profile:', error);
                showNotification('حدث خطأ أثناء حفظ الملف الشخصي', 'error');
            }
        }

        function resetProfile() {
            if (confirm('هل أنت متأكد من إعادة تعيين الملف الشخصي؟')) {
                loadProfile();
                showNotification('تم إعادة تعيين الملف الشخصي');
            }
        }

        // Profile functions
        function showProfile() {
            console.log('Showing profile');
            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            document.getElementById('mainApp').style.display = 'none';
            document.getElementById('profileScreen').style.display = 'block';
            loadProfileData();
        }

        function closeProfile() {
            console.log('Closing profile');
            document.getElementById('profileScreen').style.display = 'none';
            document.getElementById('mainApp').style.display = 'block';

            // Reset edit mode
            if (document.getElementById('editActions').style.display !== 'none') {
                cancelEdit();
            }
        }

        async function loadProfileData() {
            try {
                console.log('Loading profile data for user:', currentUser.id);

                // Get fresh user data
                const userData = await pb.collection('users').getOne(currentUser.id);
                currentUser = userData;

                // Get profile data
                let profileData = null;
                try {
                    const profiles = await pb.collection('profiles').getList(1, 1, {
                        filter: `user.id = "${currentUser.id}"`,
                        expand: 'user'
                    });

                    if (profiles.items.length > 0) {
                        profileData = profiles.items[0];
                    }
                } catch (profileError) {
                    console.warn('Could not load profile:', profileError);
                }

                // Create profile if doesn't exist
                if (!profileData) {
                    try {
                        console.log('Creating new profile for user:', currentUser.id);
                        profileData = await pb.collection('profiles').create({
                            user: currentUser.id,
                            displayName: '',
                            job: '',
                            phone: '',
                            bio: '',
                            instagram: '',
                            youtube: ''
                        });
                        console.log('New profile created:', profileData);
                    } catch (createError) {
                        console.error('Could not create profile:', createError);
                        console.error('Create error details:', createError.response || createError);

                        // Create a temporary profile object for display
                        profileData = {
                            id: null, // No ID means we can't save
                            displayName: '',
                            job: '',
                            phone: '',
                            bio: '',
                            instagram: '',
                            youtube: '',
                            avatar: null
                        };

                        showNotification('تعذر إنشاء البروفايل. قد تحتاج لإعادة تسجيل الدخول.', 'error');
                    }
                }

                // Update profile display
                updateProfileDisplay(userData, profileData);

            } catch (error) {
                console.error('Failed to load profile data:', error);
                showNotification('خطأ في تحميل بيانات البروفايل', 'error');
            }
        }

        function updateProfileDisplay(userData, profileData) {
            // Update header info
            const displayName = profileData.displayName || userData.username;
            document.getElementById('profileDisplayName').textContent = displayName;
            document.getElementById('profileUsername').textContent = `@${userData.username}`;
            document.getElementById('profileEmail').textContent = userData.email;

            // Update avatar
            updateProfileAvatar(profileData.avatar);

            // Update profile fields
            updateFieldDisplay('displayNameView', profileData.displayName, 'لم يتم تعيين اسم عرض');
            updateFieldDisplay('jobView', profileData.job, 'لم يتم تحديد المهنة');
            updateFieldDisplay('phoneView', profileData.phone, 'لم يتم إضافة رقم هاتف');
            updateFieldDisplay('bioView', profileData.bio, 'لم يتم إضافة نبذة شخصية');
            updateFieldDisplay('instagramView', profileData.instagram, 'لم يتم إضافة رابط');
            updateFieldDisplay('youtubeView', profileData.youtube, 'لم يتم إضافة رابط');

            // Store profile data for editing
            window.currentProfile = profileData;
        }

        function updateFieldDisplay(elementId, value, emptyText) {
            const element = document.getElementById(elementId);
            if (value && value.trim()) {
                element.textContent = value;
                element.classList.remove('empty');
            } else {
                element.textContent = emptyText;
                element.classList.add('empty');
            }
        }

        function updateProfileAvatar(avatarUrl) {
            const avatarImg = document.getElementById('profileAvatarImg');
            const avatarPlaceholder = document.getElementById('profileAvatarPlaceholder');

            if (avatarUrl) {
                try {
                    avatarImg.src = pb.files.getUrl(window.currentProfile, avatarUrl);
                    avatarImg.style.display = 'block';
                    avatarPlaceholder.style.display = 'none';
                } catch (error) {
                    console.warn('Could not load avatar:', error);
                    avatarImg.style.display = 'none';
                    avatarPlaceholder.style.display = 'block';
                }
            } else {
                avatarImg.style.display = 'none';
                avatarPlaceholder.style.display = 'block';
            }
        }

        // Profile editing functions
        function toggleEdit() {
            const editActions = document.getElementById('editActions');
            const avatarOverlay = document.getElementById('avatarEditOverlay');

            if (editActions.style.display === 'none') {
                startEdit();
            } else {
                cancelEdit();
            }
        }

        function startEdit() {
            console.log('Starting edit mode');

            // Check if profile exists and has ID
            if (!window.currentProfile || !window.currentProfile.id) {
                showNotification('لا يمكن تعديل البروفايل. يرجى إعادة تحميل الصفحة.', 'error');
                return;
            }

            // Show edit elements
            document.getElementById('editActions').style.display = 'flex';
            document.getElementById('avatarEditOverlay').style.display = 'block';

            // Hide view elements and show edit inputs
            const fields = ['displayName', 'job', 'phone', 'bio', 'instagram', 'youtube'];
            fields.forEach(field => {
                document.getElementById(field + 'View').style.display = 'none';
                document.getElementById(field + 'Edit').style.display = 'block';

                // Set current values
                const currentValue = window.currentProfile ? window.currentProfile[field] || '' : '';
                document.getElementById(field + 'Edit').value = currentValue;
            });

            // Update edit button icon
            document.getElementById('editProfileBtn').innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M18 6L6 18"/>
                    <path d="M6 6l12 12"/>
                </svg>
            `;
        }

        function cancelEdit() {
            console.log('Cancelling edit mode');

            // Hide edit elements
            document.getElementById('editActions').style.display = 'none';
            document.getElementById('avatarEditOverlay').style.display = 'none';

            // Show view elements and hide edit inputs
            const fields = ['displayName', 'job', 'phone', 'bio', 'instagram', 'youtube'];
            fields.forEach(field => {
                document.getElementById(field + 'View').style.display = 'block';
                document.getElementById(field + 'Edit').style.display = 'none';
            });

            // Reset edit button icon
            document.getElementById('editProfileBtn').innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                </svg>
            `;
        }

        async function saveProfile() {
            const saveBtn = document.getElementById('saveProfileBtn');
            const originalText = saveBtn.textContent;

            try {
                saveBtn.disabled = true;
                saveBtn.textContent = 'جاري الحفظ...';

                console.log('Starting profile save...');
                console.log('Current user:', currentUser);
                console.log('Current profile:', window.currentProfile);

                if (!currentUser) {
                    throw new Error('المستخدم غير مسجل الدخول');
                }

                if (!window.currentProfile) {
                    throw new Error('بيانات البروفايل غير متوفرة');
                }

                // Get form data
                const formData = {
                    displayName: document.getElementById('displayNameEdit').value.trim(),
                    job: document.getElementById('jobEdit').value.trim(),
                    phone: document.getElementById('phoneEdit').value.trim(),
                    bio: document.getElementById('bioEdit').value.trim(),
                    instagram: document.getElementById('instagramEdit').value.trim(),
                    youtube: document.getElementById('youtubeEdit').value.trim()
                };

                console.log('Form data to save:', formData);
                console.log('Profile ID:', window.currentProfile.id);

                // Validate profile ID
                if (!window.currentProfile.id) {
                    throw new Error('معرف البروفايل غير صحيح');
                }

                // Update profile
                console.log('Updating profile...');
                const updatedProfile = await pb.collection('profiles').update(window.currentProfile.id, formData);
                console.log('Profile updated successfully:', updatedProfile);

                // Update stored profile data
                window.currentProfile = updatedProfile;

                // Update display
                updateProfileDisplay(currentUser, updatedProfile);

                // Exit edit mode
                cancelEdit();

                showNotification('تم حفظ التغييرات بنجاح!', 'success');

            } catch (error) {
                console.error('Failed to save profile:', error);
                console.error('Error details:', error.response || error);

                let errorMessage = 'خطأ غير معروف';
                if (error.message) {
                    errorMessage = error.message;
                } else if (error.response && error.response.message) {
                    errorMessage = error.response.message;
                }

                showNotification('خطأ في حفظ التغييرات: ' + errorMessage, 'error');
            } finally {
                saveBtn.disabled = false;
                saveBtn.textContent = originalText;
            }
        }

        // Avatar upload handling
        document.addEventListener('DOMContentLoaded', function() {
            const avatarInput = document.getElementById('avatarInput');
            if (avatarInput) {
                avatarInput.addEventListener('change', handleAvatarUpload);
            }
        });

        async function handleAvatarUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Validate file
            if (!file.type.startsWith('image/')) {
                showNotification('يرجى اختيار ملف صورة صحيح', 'error');
                return;
            }

            if (file.size > 5 * 1024 * 1024) {
                showNotification('حجم الصورة كبير جداً. الحد الأقصى 5 ميجابايت', 'error');
                return;
            }

            try {
                showNotification('جاري رفع الصورة...', 'info');

                // Create FormData
                const formData = new FormData();
                formData.append('avatar', file);

                // Update profile with new avatar
                const updatedProfile = await pb.collection('profiles').update(window.currentProfile.id, formData);
                console.log('Avatar updated successfully:', updatedProfile);

                // Update stored profile data
                window.currentProfile = updatedProfile;

                // Update avatar display
                updateProfileAvatar(updatedProfile.avatar);

                showNotification('تم تحديث الصورة الشخصية بنجاح', 'success');

            } catch (error) {
                console.error('Avatar upload failed:', error);
                showNotification('خطأ في رفع الصورة: ' + error.message, 'error');
            }

            // Clear input
            event.target.value = '';
        }

        // Logout function
        function logout() {
            console.log('Logout requested');

            // Show confirmation dialog
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                try {
                    console.log('Logging out user...');

                    // Clear PocketBase auth
                    pb.authStore.clear();

                    // Clear current user
                    currentUser = null;
                    window.currentProfile = null;

                    // Hide profile screen and show login screen
                    document.getElementById('profileScreen').style.display = 'none';
                    document.getElementById('mainApp').style.display = 'none';
                    document.getElementById('loginScreen').style.display = 'flex';

                    // Reset any edit mode
                    if (document.getElementById('editActions').style.display !== 'none') {
                        cancelEdit();
                    }

                    // Clear login form
                    document.getElementById('emailInput').value = '';
                    document.getElementById('passwordInput').value = '';

                    showNotification('تم تسجيل الخروج بنجاح', 'success');
                    console.log('Logout successful');

                } catch (error) {
                    console.error('Logout error:', error);
                    showNotification('خطأ في تسجيل الخروج', 'error');
                }
            }
        }

        // Add Appointment functions
        function showAddAppointment() {
            console.log('Showing add appointment screen');
            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            document.getElementById('mainApp').style.display = 'none';
            document.getElementById('addAppointmentScreen').style.display = 'block';

            // Reset form
            resetAddAppointmentForm();

            // Set default time to current time + 1 hour
            const now = new Date();
            now.setHours(now.getHours() + 1);
            const timeString = now.toTimeString().slice(0, 5);
            document.getElementById('timeInput').value = timeString;
        }

        function closeAddAppointment() {
            console.log('Closing add appointment screen');
            document.getElementById('addAppointmentScreen').style.display = 'none';
            document.getElementById('mainApp').style.display = 'block';

            // Reset form
            resetAddAppointmentForm();
        }

        function resetAddAppointmentForm() {
            // Clear form
            document.getElementById('addAppointmentForm').reset();

            // Reset privacy to open
            document.querySelector('input[name="privacy"][value="open"]').checked = true;
            updatePrivacySelection();

            // Reset calendar to gregorian
            currentCalendarType = 'gregorian';
            document.querySelector('input[name="calendarType"][value="gregorian"]').checked = true;
            document.querySelectorAll('.calendar-option').forEach(opt => opt.classList.remove('selected'));
            document.getElementById('gregorianOption').classList.add('selected');

            // Reset calendar
            setupCalendarType();
            resetDateDisplays();

            // Clear selected guests
            window.selectedGuests = [];
            updateGuestsList();

            // Hide search results
            document.getElementById('searchResults').style.display = 'none';
            document.getElementById('guestSearch').value = '';

            // Reset time input
            document.getElementById('timeInput').value = '';
        }

        // Privacy and Calendar selection handling
        document.addEventListener('DOMContentLoaded', function() {
            // Privacy options
            const privacyOptions = document.querySelectorAll('.privacy-option-compact');
            privacyOptions.forEach(option => {
                option.addEventListener('click', function() {
                    privacyOptions.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    this.querySelector('input[type="radio"]').checked = true;
                });
            });

            // Initialize calendar
            initializeCalendar();
        });

        function updatePrivacySelection() {
            const privacyOptions = document.querySelectorAll('.privacy-option-compact');
            privacyOptions.forEach(option => {
                const radio = option.querySelector('input[type="radio"]');
                if (radio && radio.checked) {
                    option.classList.add('selected');
                } else {
                    option.classList.remove('selected');
                }
            });
        }

        // Guest management
        window.selectedGuests = [];

        async function searchUsers(query) {
            const resultsContainer = document.getElementById('searchResults');

            if (!query || query.trim().length < 2) {
                resultsContainer.style.display = 'none';
                return;
            }

            try {
                console.log('Searching for users:', query);

                // Search users by username or display name
                const users = await pb.collection('users').getList(1, 10, {
                    filter: `username ~ "${query}" || email ~ "${query}"`,
                    expand: 'profiles(user)'
                });

                console.log('Search results:', users);

                if (users.items.length === 0) {
                    resultsContainer.innerHTML = '<div class="search-result-item">لا توجد نتائج</div>';
                    resultsContainer.style.display = 'block';
                    return;
                }

                // Filter out current user and already selected guests
                const filteredUsers = users.items.filter(user => {
                    return user.id !== currentUser.id &&
                           !window.selectedGuests.find(guest => guest.id === user.id);
                });

                if (filteredUsers.length === 0) {
                    resultsContainer.innerHTML = '<div class="search-result-item">لا توجد مستخدمين جدد</div>';
                    resultsContainer.style.display = 'block';
                    return;
                }

                // Display results
                resultsContainer.innerHTML = filteredUsers.map(user => {
                    const profile = user.expand?.profiles?.[0];
                    const displayName = profile?.displayName || user.username;

                    return `
                        <div class="search-result-item" onclick="addGuest('${user.id}', '${user.username}', '${displayName}')">
                            <div class="result-avatar">
                                ${profile?.avatar ?
                                    `<img src="${pb.files.getUrl(profile, profile.avatar)}" alt="${displayName}">` :
                                    '👤'
                                }
                            </div>
                            <div class="result-info">
                                <div class="result-name">${displayName}</div>
                                <div class="result-username">@${user.username}</div>
                            </div>
                        </div>
                    `;
                }).join('');

                resultsContainer.style.display = 'block';

            } catch (error) {
                console.error('Search error:', error);
                resultsContainer.innerHTML = '<div class="search-result-item">خطأ في البحث</div>';
                resultsContainer.style.display = 'block';
            }
        }

        function addGuest(userId, username, displayName) {
            console.log('Adding guest:', { userId, username, displayName });

            // Check if already selected
            if (window.selectedGuests.find(guest => guest.id === userId)) {
                return;
            }

            // Add to selected guests
            window.selectedGuests.push({
                id: userId,
                username: username,
                displayName: displayName
            });

            // Update display
            updateGuestsList();

            // Clear search
            document.getElementById('guestSearch').value = '';
            document.getElementById('searchResults').style.display = 'none';
        }

        function removeGuest(userId) {
            console.log('Removing guest:', userId);
            window.selectedGuests = window.selectedGuests.filter(guest => guest.id !== userId);
            updateGuestsList();
        }

        function updateGuestsList() {
            const guestsList = document.getElementById('guestsList');

            if (window.selectedGuests.length === 0) {
                guestsList.innerHTML = '<div class="no-guests">لم يتم اختيار مدعوين بعد</div>';
                return;
            }

            guestsList.innerHTML = window.selectedGuests.map(guest => `
                <div class="guest-chip">
                    <span>${guest.displayName}</span>
                    <button class="remove-guest" onclick="removeGuest('${guest.id}')" type="button">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"/>
                            <line x1="6" y1="6" x2="18" y2="18"/>
                        </svg>
                    </button>
                </div>
            `).join('');
        }

        // Form submission handling
        document.addEventListener('DOMContentLoaded', function() {
            const addAppointmentForm = document.getElementById('addAppointmentForm');
            if (addAppointmentForm) {
                addAppointmentForm.addEventListener('submit', handleCreateAppointment);
            }
        });

        async function handleCreateAppointment(event) {
            event.preventDefault();

            const createBtn = document.getElementById('createAppointmentBtn');
            const originalText = createBtn.textContent;

            try {
                createBtn.disabled = true;
                createBtn.textContent = 'جاري إنشاء الموعد...';

                console.log('Creating appointment...');

                // Get form data
                const titleElement = document.getElementById('appointmentTitle');
                const privacyElement = document.querySelector('input[name="privacy"]:checked');
                const regionElement = document.getElementById('appointmentRegion');
                const buildingElement = document.getElementById('appointmentBuilding');

                console.log('Form elements found:', {
                    title: !!titleElement,
                    privacy: !!privacyElement,
                    region: !!regionElement,
                    building: !!buildingElement
                });

                const title = titleElement ? titleElement.value.trim() : '';
                const privacy = privacyElement ? privacyElement.value : '';
                const region = regionElement ? regionElement.value.trim() : '';
                const building = buildingElement ? buildingElement.value.trim() : '';

                console.log('Form data:', { title, privacy, region, building });

                // Validation
                if (!title) {
                    console.error('Title is required');
                    throw new Error('عنوان الموعد مطلوب');
                }

                if (!privacy) {
                    console.error('Privacy is required');
                    throw new Error('نوع الخصوصية مطلوب');
                }

                if (!region) {
                    console.error('Region is required');
                    throw new Error('المنطقة مطلوبة');
                }

                // Get date using the new calendar system
                console.log('Getting selected date...');
                const dateInfo = getSelectedDate();
                console.log('Date info received:', dateInfo);

                // Check if date is not in the past
                const appointmentDateTime = new Date(`${dateInfo.startDate}T${dateInfo.time || '00:00'}`);
                const now = new Date();
                if (appointmentDateTime <= now) {
                    throw new Error('لا يمكن إنشاء موعد في الماضي');
                }

                // Prepare appointment data
                const appointmentData = {
                    title: title,
                    privacy: privacy,
                    region: region,
                    building: building || '',
                    calendar_type: dateInfo.calendar_type,
                    primary_date: dateInfo.primary_date,
                    gregorian_date: dateInfo.gregorian_date,
                    appointment_datetime: dateInfo.appointment_datetime, // Full datetime for sorting
                    hasTime: dateInfo.hasTime,
                    time: dateInfo.time || '',
                    owner: currentUser.id,
                    guests: window.selectedGuests.map(guest => guest.id),
                    status: 'active'
                };

                console.log('NEW STRUCTURE - Appointment data to save:', {
                    calendar_type: appointmentData.calendar_type,
                    primary_date: appointmentData.primary_date,
                    gregorian_date: appointmentData.gregorian_date,
                    time: appointmentData.time
                });

                console.log('=== SAVING APPOINTMENT DATA ===');
                console.log('Full appointment data to save:', appointmentData);
                console.log('Calendar type being saved:', appointmentData.calendarType);
                console.log('Hijri data being saved:', {
                    year: appointmentData.hijriYear,
                    month: appointmentData.hijriMonth,
                    day: appointmentData.hijriDay
                });

                // Create appointment
                console.log('=== CREATING APPOINTMENT IN DATABASE ===');
                const appointment = await pb.collection('appointments').create(appointmentData);
                console.log('=== APPOINTMENT CREATED SUCCESSFULLY ===');
                console.log('Created appointment:', appointment);
                console.log('Saved calendarType:', appointment.calendarType);
                console.log('Saved hijri data:', {
                    year: appointment.hijriYear,
                    month: appointment.hijriMonth,
                    day: appointment.hijriDay
                });
                console.log('Appointment created:', appointment);

                // Create guest status records and send notifications
                for (const guest of window.selectedGuests) {
                    try {
                        // Create guest status record
                        await pb.collection('appointment_guests_status').create({
                            appointment: appointment.id,
                            user: guest.id,
                            status: 'invited',
                            note: ''
                        });

                        // Send notification
                        await pb.collection('inbox').create({
                            title: `دعوة لموعد: ${title}`,
                            message: `تم دعوتك لموعد "${title}" في ${region}`,
                            type: 'invitation',
                            owner: guest.id,
                            appointment: appointment.id,
                            relatedUser: currentUser.id,
                            read: false
                        });

                        console.log('Notification sent to:', guest.username);
                    } catch (notificationError) {
                        console.warn('Failed to send notification to:', guest.username, notificationError);
                    }
                }

                showNotification('تم إنشاء الموعد بنجاح!', 'success');
                closeAddAppointment();

                // Refresh appointments list
                loadAppointments();

            } catch (error) {
                console.error('Failed to create appointment:', error);
                showNotification('خطأ في إنشاء الموعد: ' + error.message, 'error');
            } finally {
                createBtn.disabled = false;
                createBtn.textContent = originalText;
            }
        }

        // Appointments management
        let allAppointments = [];
        let currentFilter = 'all';

        async function loadAppointments() {
            try {
                console.log('Loading appointments for user:', currentUser.id);
                showAppointmentsLoading();

                if (!currentUser) {
                    throw new Error('User not logged in');
                }

                // First, try to load all appointments to see what's available
                console.log('Loading all appointments...');
                const allAppointmentsTest = await pb.collection('appointments').getList(1, 10, {
                    expand: 'owner,guests',
                    sort: '-created'
                });
                console.log('=== ALL APPOINTMENTS IN DATABASE ===');
                console.log('Total appointments:', allAppointmentsTest.totalItems);
                allAppointmentsTest.items.forEach((apt, index) => {
                    console.log(`Appointment ${index + 1}:`, {
                        id: apt.id,
                        title: apt.title,
                        calendarType: apt.calendarType,
                        startDate: apt.startDate,
                        hijriYear: apt.hijriYear,
                        hijriMonth: apt.hijriMonth,
                        hijriDay: apt.hijriDay
                    });
                });

                // Load appointments where user is owner - SORTED BY GREGORIAN_DATE
                console.log('Loading my appointments...');
                const myAppointments = await pb.collection('appointments').getList(1, 50, {
                    filter: `owner = "${currentUser.id}"`,
                    expand: 'owner,guests',
                    sort: '+gregorian_date'  // Sort by gregorian_date ascending (closest first)
                });
                console.log('My appointments result:', myAppointments);

                // Load appointments where user is in guests array
                console.log('Loading invited appointments...');
                let invitedAppointments = { items: [] };
                try {
                    invitedAppointments = await pb.collection('appointments').getList(1, 50, {
                        filter: `guests ~ "${currentUser.id}"`,
                        expand: 'owner,guests',
                        sort: '+gregorian_date'  // Sort by gregorian_date ascending
                    });
                    console.log('Invited appointments result:', invitedAppointments);
                } catch (inviteError) {
                    console.warn('Could not load invited appointments:', inviteError);
                    // Try alternative filter
                    try {
                        invitedAppointments = await pb.collection('appointments').getList(1, 50, {
                            expand: 'owner,guests',
                            sort: '+gregorian_date'  // Sort by gregorian_date ascending
                        });
                        // Filter manually
                        invitedAppointments.items = invitedAppointments.items.filter(apt =>
                            apt.guests && apt.guests.includes(currentUser.id)
                        );
                        console.log('Manually filtered invited appointments:', invitedAppointments);
                    } catch (manualError) {
                        console.error('Manual filter also failed:', manualError);
                    }
                }

                // Combine and deduplicate
                const appointmentsMap = new Map();

                myAppointments.items.forEach(apt => {
                    console.log('Adding my appointment:', apt.title);
                    apt.userRole = 'owner';
                    appointmentsMap.set(apt.id, apt);
                });

                invitedAppointments.items.forEach(apt => {
                    if (!appointmentsMap.has(apt.id)) {
                        console.log('Adding invited appointment:', apt.title);
                        apt.userRole = 'guest';
                        appointmentsMap.set(apt.id, apt);
                    }
                });

                allAppointments = Array.from(appointmentsMap.values());
                console.log('Final appointments array (before sorting):', allAppointments);
                console.log('Total appointments found:', allAppointments.length);

                // Appointments are already sorted by database query (+gregorian_date)
                console.log('📋 APPOINTMENTS LOADED (sorted by database):');
                console.log('Total appointments:', allAppointments.length);

                if (allAppointments.length > 0) {
                    // Show appointments in database sort order
                    allAppointments.forEach((apt, index) => {
                        const dateStr = apt.gregorian_date || 'No date';
                        const timeStr = apt.time || 'No time';
                        console.log(`${index + 1}. "${apt.title}" - ${dateStr} ${timeStr}`);
                    });
                    console.log('✅ Appointments displayed in database sort order');
                } else {
                    console.log('No appointments found');
                }

                console.log('Final appointments array (after sorting):', allAppointments.map(apt => ({
                    title: apt.title,
                    date: apt.gregorian_date || apt.startDate,
                    time: apt.time
                })));

                displayAppointments();

            } catch (error) {
                console.error('Failed to load appointments:', error);
                console.error('Error details:', error.response || error);
                showAppointmentsError();
            }
        }

        function showAppointmentsLoading() {
            document.getElementById('appointmentsLoading').style.display = 'block';
            document.getElementById('appointmentsEmpty').style.display = 'none';
            document.getElementById('appointmentsList').style.display = 'none';
        }

        function showAppointmentsError() {
            document.getElementById('appointmentsLoading').style.display = 'none';
            document.getElementById('appointmentsEmpty').style.display = 'block';
            document.getElementById('appointmentsList').style.display = 'none';

            // Update empty state for error
            document.querySelector('.empty-state h3').textContent = 'خطأ في تحميل المواعيد';
            document.querySelector('.empty-state p').textContent = 'تعذر تحميل المواعيد. يرجى المحاولة مرة أخرى.';
        }

        function displayAppointments() {
            console.log('Displaying appointments...');
            console.log('All appointments:', allAppointments);
            console.log('Current filter:', currentFilter);

            const filteredAppointments = filterAppointmentsByType(allAppointments, currentFilter);
            console.log('Filtered appointments:', filteredAppointments);
            console.log('Filtered count:', filteredAppointments.length);

            document.getElementById('appointmentsLoading').style.display = 'none';

            if (filteredAppointments.length === 0) {
                console.log('No appointments to display, showing empty state');
                document.getElementById('appointmentsEmpty').style.display = 'block';
                document.getElementById('appointmentsList').style.display = 'none';

                // Update empty state message based on filter
                const emptyTitle = document.querySelector('.empty-state h3');
                const emptyDesc = document.querySelector('.empty-state p');

                switch(currentFilter) {
                    case 'my':
                        emptyTitle.textContent = 'لا توجد مواعيد منشأة';
                        emptyDesc.textContent = 'لم تقم بإنشاء أي مواعيد بعد. ابدأ بإنشاء موعد جديد.';
                        break;
                    case 'invited':
                        emptyTitle.textContent = 'لا توجد دعوات';
                        emptyDesc.textContent = 'لم تتلق أي دعوات للمواعيد بعد.';
                        break;
                    default:
                        emptyTitle.textContent = 'لا توجد مواعيد';
                        emptyDesc.textContent = 'ابدأ بإنشاء موعد جديد أو انتظر دعوة من الأصدقاء.';
                }
                return;
            }

            console.log('Showing appointments list');
            document.getElementById('appointmentsEmpty').style.display = 'none';
            document.getElementById('appointmentsList').style.display = 'block';

            const appointmentsList = document.getElementById('appointmentsList');
            console.log('Creating appointment cards...');

            try {
                const cardsHTML = filteredAppointments.map((appointment, index) => {
                    console.log(`Creating card ${index + 1} for appointment:`, appointment.title);
                    return createAppointmentCard(appointment);
                }).join('');

                console.log('Generated HTML length:', cardsHTML.length);
                appointmentsList.innerHTML = cardsHTML;
                console.log('Appointments displayed successfully');
            } catch (error) {
                console.error('Error creating appointment cards:', error);
                appointmentsList.innerHTML = '<div style="padding: 20px; text-align: center; color: red;">خطأ في عرض المواعيد</div>';
            }
        }

        function filterAppointmentsByType(appointments, filter) {
            switch(filter) {
                case 'my':
                    return appointments.filter(apt => apt.userRole === 'owner');
                case 'invited':
                    return appointments.filter(apt => apt.userRole === 'guest');
                default:
                    return appointments;
            }
        }

        function filterAppointments(filter) {
            console.log('Filtering appointments:', filter);
            currentFilter = filter;

            // Update filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

            displayAppointments();
        }

        function createAppointmentCard(appointment) {
            console.log('=== Creating card for appointment ===');
            console.log('Full appointment object:', appointment);
            console.log('Calendar type from DB:', appointment.calendarType);
            console.log('Hijri data from DB:', {
                year: appointment.hijriYear,
                month: appointment.hijriMonth,
                day: appointment.hijriDay
            });
            console.log('Start date from DB:', appointment.startDate);

            try {
                const now = new Date();
                let appointmentDate;

                // Handle date parsing more safely
                try {
                    appointmentDate = new Date(`${appointment.startDate}T${appointment.time || '00:00'}`);
                    if (isNaN(appointmentDate.getTime())) {
                        appointmentDate = new Date(appointment.startDate);
                    }
                } catch (dateError) {
                    console.warn('Date parsing error:', dateError);
                    appointmentDate = new Date();
                }

                const isPast = appointmentDate < now;

                // Format date based on calendar type
                let formattedDate = '';

                // NEW APPROACH: Display both dates together
                console.log('🔍 Preparing dual date display...');

                let gregorianDate = '';
                let hijriDate = '';

                // Format Gregorian date in Arabic
                const gregorianMonths = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                                       'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

                const day = appointmentDate.getDate();
                const month = appointmentDate.getMonth();
                const year = appointmentDate.getFullYear();
                const weekday = appointmentDate.getDay();

                gregorianDate = `${weekdays[weekday]}، ${day} ${gregorianMonths[month]} ${year} م`;
                console.log('✅ Gregorian date formatted in Arabic:', gregorianDate);

                // NEW STRUCTURE: Use primary_date from database
                console.log('🔍 NEW STRUCTURE - Reading appointment data:');
                console.log('📊 appointment.calendar_type:', appointment.calendar_type);
                console.log('📊 appointment.primary_date:', appointment.primary_date);
                console.log('📊 appointment.gregorian_date:', appointment.gregorian_date);

                let primaryDate = '';
                let secondaryDate = '';

                if (appointment.primary_date && appointment.primary_date.formatted) {
                    // Use the saved primary date (user's original choice)
                    primaryDate = appointment.primary_date.formatted;
                    console.log('✅ Using saved primary date:', primaryDate);

                    // Generate secondary date (opposite calendar)
                    if (appointment.primary_date.type === 'hijri') {
                        // Primary is Hijri, secondary is Gregorian
                        try {
                            // Use the saved primary date values to convert to Gregorian
                            const hijriYear = appointment.primary_date.year;
                            const hijriMonth = appointment.primary_date.month;
                            const hijriDay = appointment.primary_date.day;

                            console.log('Converting Hijri to Gregorian:', {hijriYear, hijriMonth, hijriDay});

                            const convertedGregorian = hijriToGregorian(hijriYear, hijriMonth, hijriDay);
                            console.log('Converted Gregorian:', convertedGregorian);

                            const gregorianMonths = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                                                   'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
                            const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

                            // Secondary date WITHOUT weekday
                            secondaryDate = `${convertedGregorian.day} ${gregorianMonths[convertedGregorian.month - 1]} ${convertedGregorian.year}`;
                            console.log('🌙 Primary: Hijri (user choice), Secondary: Gregorian reference:', secondaryDate);
                        } catch (conversionError) {
                            console.warn('❌ Gregorian conversion error:', conversionError);
                            secondaryDate = '';
                        }
                    } else {
                        // Primary is Gregorian, secondary is Hijri
                        try {
                            // Use the saved primary date values to convert to Hijri
                            const gregorianYear = appointment.primary_date.year;
                            const gregorianMonth = appointment.primary_date.month;
                            const gregorianDay = appointment.primary_date.day;

                            console.log('Converting Gregorian to Hijri:', {gregorianYear, gregorianMonth, gregorianDay});

                            const convertedHijri = gregorianToHijri(gregorianYear, gregorianMonth, gregorianDay);
                            console.log('Converted Hijri:', convertedHijri);

                            const hijriMonthName = getHijriMonthName(convertedHijri.month);
                            secondaryDate = `${convertedHijri.day} ${hijriMonthName} ${convertedHijri.year}`;
                            console.log('📅 Primary: Gregorian (user choice), Secondary: Hijri reference:', secondaryDate);
                        } catch (conversionError) {
                            console.warn('❌ Hijri conversion error:', conversionError);
                            secondaryDate = '';
                        }
                    }
                } else {
                    // Fallback for old data structure
                    console.log('⚠️ Fallback: Using old data structure');
                    primaryDate = gregorianDate;
                    secondaryDate = '';
                }

                // Format: Primary Date (Secondary Date) - Time
                if (secondaryDate) {
                    formattedDate = `${primaryDate} <span class="secondary-date">(${secondaryDate})</span>`;
                } else {
                    formattedDate = primaryDate;
                }
                console.log('✅ Final formatted date:', formattedDate);

                let formattedTime = '';
                if (appointment.hasTime && appointment.time) {
                    try {
                        // Parse the time string (HH:MM format)
                        const [hours, minutes] = appointment.time.split(':');
                        const hour24 = parseInt(hours);
                        const minute = parseInt(minutes);

                        // Convert to 12-hour format with Arabic AM/PM
                        let hour12 = hour24;
                        let period = 'ص'; // صباحاً

                        if (hour24 === 0) {
                            hour12 = 12;
                            period = 'ص';
                        } else if (hour24 === 12) {
                            hour12 = 12;
                            period = 'م';
                        } else if (hour24 > 12) {
                            hour12 = hour24 - 12;
                            period = 'م';
                        }

                        formattedTime = `${hour12}:${String(minute).padStart(2, '0')} ${period}`;
                        console.log(`Time formatting: ${appointment.time} → ${formattedTime}`);
                    } catch (timeError) {
                        console.warn('Time formatting error:', timeError);
                        formattedTime = appointment.time;
                    }
                }

                // Get guests info safely
                const guests = appointment.expand?.guests || [];
                const guestCount = guests.length;
                console.log('Guests for appointment:', guests);

                // Privacy icon
                const privacyIcon = appointment.privacy === 'open' ? '🌍' : '🔒';
                const privacyText = appointment.privacy === 'open' ? 'مفتوح' : 'خاص';

                // Role indicator
                const roleClass = appointment.userRole === 'guest' ? 'invited' : '';
                const pastClass = isPast ? 'past' : '';

                // Owner info
                const ownerName = appointment.expand?.owner?.username || 'غير معروف';

                console.log('Card data prepared:', {
                    title: appointment.title,
                    date: formattedDate,
                    time: formattedTime,
                    privacy: privacyText,
                    guestCount,
                    owner: ownerName
                });

                // Calculate countdown
                const timeUntil = appointmentDate - now;
                const daysUntil = Math.ceil(timeUntil / (1000 * 60 * 60 * 24));
                const hoursUntil = Math.ceil(timeUntil / (1000 * 60 * 60));

                let countdownText = '';
                let countdownClass = '';

                if (isPast) {
                    countdownText = 'انتهى';
                    countdownClass = 'countdown-past';
                } else if (daysUntil <= 0) {
                    if (hoursUntil <= 1) {
                        countdownText = 'خلال ساعة';
                        countdownClass = 'countdown-critical';
                    } else {
                        countdownText = `خلال ${hoursUntil} ساعة`;
                        countdownClass = 'countdown-urgent';
                    }
                } else if (daysUntil === 1) {
                    countdownText = 'غداً';
                    countdownClass = 'countdown-urgent';
                } else {
                    countdownText = `خلال ${daysUntil} يوم`;
                    countdownClass = '';
                }

                // تنسيق التاريخ والوقت
                let cardDate;
                try {
                    cardDate = new Date(`${appointment.startDate || '2024-01-15'}T${appointment.time || '07:38'}`);
                    if (isNaN(cardDate.getTime())) {
                        cardDate = new Date(); // استخدم التاريخ الحالي كبديل
                    }
                } catch (error) {
                    cardDate = new Date(); // استخدم التاريخ الحالي كبديل
                }

                const weekdaysAr = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
                const monthsAr = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

                const dayName = weekdaysAr[cardDate.getDay()] || 'الأحد';
                const dayNum = cardDate.getDate() || 15;
                const monthName = monthsAr[cardDate.getMonth()] || 'يناير';

                // تنسيق الوقت
                let timeFormatted = '';
                try {
                    const timeStr = appointment.time || '07:38';
                    const [hours, minutes] = timeStr.split(':');
                    const hour24 = parseInt(hours) || 7;
                    const mins = parseInt(minutes) || 38;

                    let hour12 = hour24;
                    let period = 'ص';

                    if (hour24 === 0) {
                        hour12 = 12;
                        period = 'ص';
                    } else if (hour24 === 12) {
                        hour12 = 12;
                        period = 'م';
                    } else if (hour24 > 12) {
                        hour12 = hour24 - 12;
                        period = 'م';
                    }

                    timeFormatted = `${hour12}:${String(mins).padStart(2, '0')} ${period}`;
                } catch (error) {
                    timeFormatted = '7:38 ص'; // وقت افتراضي
                }

                const cardHTML = `
                    <div class="appointment-content-box">
                        <!-- الحاوية الداخلية -->
                        <div class="inner-container">
                            <!-- صورة المالك لاختبار الـ padding -->
                            <img src="https://via.placeholder.com/48x48/4CAF50/white?text=👤"
                                 alt="مالك الموعد"
                                 class="owner-avatar-test">

                            <!-- تايتل الموعد على نفس الخط الأفقي -->
                            <h3 class="appointment-title">${appointment.title || 'عنوان الموعد العريض واللافت'}</h3>

                            <!-- المنطقة والعنوان تحت التايتل -->
                            <p class="appointment-location">${appointment.region || 'المنطقة'} - ${appointment.building || 'العنوان'}</p>

                            <!-- التاريخ والوقت بالتنسيق المطلوب -->
                            <p class="appointment-datetime">${dayName} (${dayNum} ${monthName}) ${timeFormatted}</p>
                        </div>
                    </div>
                `;

                console.log('Generated card HTML for:', appointment.title);
                return cardHTML;

            } catch (error) {
                console.error('Error creating appointment card:', error);
                return `
                    <div class="appointment-card">
                        <div class="appointment-header">
                            <h3 class="appointment-title">خطأ في عرض الموعد</h3>
                        </div>
                        <div class="appointment-details">
                            <div class="appointment-detail">
                                <span>تعذر عرض تفاصيل هذا الموعد</span>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // Appointment actions
        function editAppointment(appointmentId) {
            showNotification('تعديل الموعد قريباً...', 'info');
        }

        function deleteAppointment(appointmentId) {
            if (confirm('هل أنت متأكد من حذف هذا الموعد؟')) {
                showNotification('حذف الموعد قريباً...', 'info');
            }
        }

        function respondToInvitation(appointmentId, response) {
            const responseText = response === 'accepted' ? 'قبول' : 'رفض';
            showNotification(`${responseText} الدعوة قريباً...`, 'info');
        }

        function viewAppointmentDetails(appointmentId) {
            showNotification('عرض تفاصيل الموعد قريباً...', 'info');
        }

        // Simplified Calendar Management
        let currentCalendarType = 'gregorian';
        let selectedDate = null;

        // Month names
        const gregorianMonths = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];

        const hijriMonths = [
            'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني',
            'جمادى الأولى', 'جمادى الثانية', 'رجب', 'شعبان',
            'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
        ];

        function initializeCalendar() {
            console.log('Initializing simplified calendar...');

            // Set default to Gregorian
            currentCalendarType = 'gregorian';
            setupCalendarType();

            // Add event listeners
            document.getElementById('gregorianOption').addEventListener('click', () => switchCalendarType('gregorian'));
            document.getElementById('hijriOption').addEventListener('click', () => switchCalendarType('hijri'));

            document.getElementById('daySelect').addEventListener('change', updateDateDisplay);
            document.getElementById('monthSelect').addEventListener('change', function() {
                updateDayOptions();
                updateDateDisplay();
            });
            document.getElementById('yearSelect').addEventListener('change', function() {
                updateDayOptions();
                updateDateDisplay();
            });

            console.log('Calendar initialized successfully');
        }

        function switchCalendarType(type) {
            console.log('Switching to', type, 'calendar');
            currentCalendarType = type;

            // Update UI
            document.querySelectorAll('.calendar-option').forEach(opt => opt.classList.remove('selected'));
            document.getElementById(type + 'Option').classList.add('selected');
            document.querySelector(`input[value="${type}"]`).checked = true;

            // Setup calendar for selected type
            setupCalendarType();

            // Reset displays
            resetDateDisplays();
        }

        function setupCalendarType() {
            const monthSelect = document.getElementById('monthSelect');
            const yearSelect = document.getElementById('yearSelect');
            const conversionLabel = document.getElementById('conversionLabel');

            // Clear existing options
            monthSelect.innerHTML = '<option value="">اختر الشهر</option>';
            yearSelect.innerHTML = '<option value="">اختر السنة</option>';

            if (currentCalendarType === 'gregorian') {
                // Setup Gregorian calendar
                conversionLabel.textContent = 'يوافق بالهجري:';

                // Add months
                gregorianMonths.forEach((month, index) => {
                    const option = document.createElement('option');
                    option.value = index + 1;
                    option.textContent = month;
                    monthSelect.appendChild(option);
                });

                // Add years (current year + 10 years)
                const currentYear = new Date().getFullYear();
                for (let year = currentYear; year <= currentYear + 10; year++) {
                    const option = document.createElement('option');
                    option.value = year;
                    option.textContent = year + ' م';
                    yearSelect.appendChild(option);
                }

                // Set current month/year as default
                const currentMonth = new Date().getMonth() + 1;
                monthSelect.value = currentMonth;
                yearSelect.value = currentYear;

            } else {
                // Setup Hijri calendar
                conversionLabel.textContent = 'يوافق بالميلادي:';

                // Add months
                hijriMonths.forEach((month, index) => {
                    const option = document.createElement('option');
                    option.value = index + 1;
                    option.textContent = month;
                    monthSelect.appendChild(option);
                });

                // Add years (current Hijri year + 10 years)
                try {
                    const currentYear = new Date().getFullYear();
                    const currentHijriYear = gregorianToHijri(currentYear, 1, 1).year;
                    for (let year = currentHijriYear; year <= currentHijriYear + 10; year++) {
                        const option = document.createElement('option');
                        option.value = year;
                        option.textContent = year + ' هـ';
                        yearSelect.appendChild(option);
                    }

                    // Set current Hijri month/year as default
                    const currentMonth = new Date().getMonth() + 1;
                    const currentHijriDate = gregorianToHijri(currentYear, currentMonth, 1);
                    monthSelect.value = currentHijriDate.month;
                    yearSelect.value = currentHijriDate.year;
                } catch (error) {
                    console.error('Error setting up Hijri calendar:', error);
                    // Fallback
                    for (let year = 1446; year <= 1456; year++) {
                        const option = document.createElement('option');
                        option.value = year;
                        option.textContent = year + ' هـ';
                        yearSelect.appendChild(option);
                    }
                    monthSelect.value = 1;
                    yearSelect.value = 1447;
                }
            }

            // Update day options
            updateDayOptions();
        }

        function updateDayOptions() {
            const daySelect = document.getElementById('daySelect');
            const monthSelect = document.getElementById('monthSelect');
            const yearSelect = document.getElementById('yearSelect');

            const month = parseInt(monthSelect.value);
            const year = parseInt(yearSelect.value);

            // Clear existing options
            daySelect.innerHTML = '<option value="">اختر اليوم</option>';

            if (!month || !year) return;

            let daysInMonth;
            if (currentCalendarType === 'gregorian') {
                daysInMonth = getDaysInGregorianMonth(month, year);
            } else {
                daysInMonth = getDaysInHijriMonth(month, year);
            }

            console.log(`Days in ${currentCalendarType} month ${month}/${year}:`, daysInMonth);

            // Add day options with weekday names
            for (let day = 1; day <= daysInMonth; day++) {
                let weekdayName = '';

                try {
                    let date;
                    if (currentCalendarType === 'gregorian') {
                        date = new Date(year, month - 1, day);
                    } else {
                        // Convert Hijri to Gregorian to get weekday
                        const gregorianDate = hijriToGregorian(year, month, day);
                        date = new Date(gregorianDate.year, gregorianDate.month - 1, gregorianDate.day);
                    }
                    weekdayName = getWeekdayName(date);
                } catch (error) {
                    console.warn('Error getting weekday for day', day, ':', error);
                    weekdayName = '';
                }

                const option = document.createElement('option');
                option.value = day;
                option.textContent = weekdayName ? `${weekdayName} ${day}` : day;
                daySelect.appendChild(option);
            }
        }

        function updateDateDisplay() {
            const day = parseInt(document.getElementById('daySelect').value);
            const month = parseInt(document.getElementById('monthSelect').value);
            const year = parseInt(document.getElementById('yearSelect').value);

            const weekdayDisplay = document.getElementById('weekdayDisplay');
            const conversionDisplay = document.getElementById('conversionDisplay');

            if (!day || !month || !year) {
                weekdayDisplay.textContent = 'اختر التاريخ لرؤية اليوم';
                conversionDisplay.textContent = 'اختر التاريخ';
                weekdayDisplay.classList.remove('highlight');
                conversionDisplay.classList.remove('highlight');
                selectedDate = null;
                return;
            }

            try {
                let weekdayName = '';
                let conversionText = '';

                if (currentCalendarType === 'gregorian') {
                    // Gregorian date
                    const date = new Date(year, month - 1, day);
                    weekdayName = getWeekdayName(date);

                    // Convert to Hijri
                    const hijriDate = gregorianToHijri(year, month, day);
                    conversionText = `${hijriDate.day} ${hijriMonths[hijriDate.month - 1]} ${hijriDate.year} هـ`;

                    // Store selected date
                    selectedDate = {
                        calendarType: 'gregorian',
                        day, month, year,
                        gregorianDate: `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
                    };

                } else {
                    // Hijri date
                    const gregorianDate = hijriToGregorian(year, month, day);
                    const date = new Date(gregorianDate.year, gregorianDate.month - 1, gregorianDate.day);
                    weekdayName = getWeekdayName(date);

                    // Convert to Gregorian
                    conversionText = `${gregorianDate.day} ${gregorianMonths[gregorianDate.month - 1]} ${gregorianDate.year} م`;

                    // Store selected date
                    selectedDate = {
                        calendarType: 'hijri',
                        day, month, year,
                        hijriDate: { day, month, year },
                        gregorianDate: `${gregorianDate.year}-${String(gregorianDate.month).padStart(2, '0')}-${String(gregorianDate.day).padStart(2, '0')}`
                    };
                }

                weekdayDisplay.textContent = weekdayName;
                conversionDisplay.textContent = conversionText;
                weekdayDisplay.classList.add('highlight');
                conversionDisplay.classList.add('highlight');

                console.log('Selected date:', selectedDate);

            } catch (error) {
                console.error('Error updating date display:', error);
                weekdayDisplay.textContent = 'خطأ في التاريخ';
                conversionDisplay.textContent = 'خطأ في التحويل';
                weekdayDisplay.classList.remove('highlight');
                conversionDisplay.classList.remove('highlight');
                selectedDate = null;
            }
        }

        function resetDateDisplays() {
            document.getElementById('daySelect').value = '';
            document.getElementById('weekdayDisplay').textContent = 'اختر التاريخ لرؤية اليوم';
            document.getElementById('conversionDisplay').textContent = 'اختر التاريخ';
            document.getElementById('weekdayDisplay').classList.remove('highlight');
            document.getElementById('conversionDisplay').classList.remove('highlight');
            selectedDate = null;
        }







        function getSelectedDate() {
            console.log('Getting selected date...');
            console.log('selectedDate variable:', selectedDate);

            if (!selectedDate) {
                console.error('No date selected');
                throw new Error('يرجى اختيار التاريخ أولاً');
            }

            const timeInput = document.getElementById('timeInput');
            console.log('Time input element:', timeInput);

            const time = timeInput ? timeInput.value : '';
            console.log('Selected time:', time);

            if (!time) {
                console.error('No time selected');
                throw new Error('الوقت مطلوب');
            }

            // NEW STRUCTURE: Build primary_date object based on user's choice
            let primaryDate = {};
            let gregorianDate = selectedDate.gregorianDate;

            // Calculate weekday for the primary date (always add to primary)
            const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
            let weekday = '';

            if (selectedDate.calendarType === 'hijri') {
                // For Hijri dates, convert to Gregorian first to get correct weekday
                try {
                    const gregorianEquivalent = hijriToGregorian(selectedDate.year, selectedDate.month, selectedDate.day);
                    const date = new Date(gregorianEquivalent.year, gregorianEquivalent.month - 1, gregorianEquivalent.day);
                    weekday = weekdays[date.getDay()];
                } catch (error) {
                    console.warn('Error getting weekday for Hijri date:', error);
                    weekday = '';
                }

                // User chose Hijri - this is the primary date WITH weekday
                primaryDate = {
                    type: 'hijri',
                    year: selectedDate.year,
                    month: selectedDate.month,
                    day: selectedDate.day,
                    formatted: weekday ? `${weekday}، ${selectedDate.day} ${getHijriMonthName(selectedDate.month)} ${selectedDate.year} هـ`
                                      : `${selectedDate.day} ${getHijriMonthName(selectedDate.month)} ${selectedDate.year} هـ`
                };
                console.log('Primary date (Hijri with weekday):', primaryDate);
            } else {
                // For Gregorian dates, calculate weekday directly
                const date = new Date(selectedDate.year, selectedDate.month - 1, selectedDate.day);
                weekday = weekdays[date.getDay()];

                const gregorianMonths = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                                       'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

                // User chose Gregorian - this is the primary date WITH weekday
                primaryDate = {
                    type: 'gregorian',
                    year: selectedDate.year,
                    month: selectedDate.month,
                    day: selectedDate.day,
                    formatted: `${weekday}، ${selectedDate.day} ${gregorianMonths[selectedDate.month - 1]} ${selectedDate.year} م`
                };
                console.log('Primary date (Gregorian with weekday):', primaryDate);
            }

            // Calculate the exact appointment datetime for sorting
            const appointmentDateTime = new Date(`${gregorianDate}T${time}:00`);

            const result = {
                calendar_type: selectedDate.calendarType,
                primary_date: primaryDate,
                gregorian_date: gregorianDate,
                appointment_datetime: appointmentDateTime.toISOString(), // Full datetime for sorting
                time: time,
                hasTime: true
            };

            console.log('📅 APPOINTMENT DATETIME CALCULATED:', {
                gregorian_date: gregorianDate,
                time: time,
                appointment_datetime: result.appointment_datetime,
                readable: appointmentDateTime.toLocaleString('ar-SA')
            });

            console.log('Final selected date for saving (NEW STRUCTURE):', result);
            return result;
        }

        console.log('Sijilli App JavaScript loaded successfully');
    </script>
