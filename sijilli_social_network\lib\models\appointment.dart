import 'user.dart';

class Appointment {
  final String id;
  final String title;
  final AppointmentPrivacy privacy;
  final String region;
  final String? building;
  final DateTime datetime;
  final String time;
  final User owner;
  final List<User> guests;
  final DateTime created;
  final DateTime updated;

  Appointment({
    required this.id,
    required this.title,
    required this.privacy,
    required this.region,
    this.building,
    required this.datetime,
    required this.time,
    required this.owner,
    required this.guests,
    required this.created,
    required this.updated,
  });

  factory Appointment.fromJson(Map<String, dynamic> json) {
    return Appointment(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      privacy: AppointmentPrivacy.fromString(json['privacy'] ?? 'private'),
      region: json['region'] ?? '',
      building: json['building'],
      datetime: DateTime.parse(json['datetime']),
      time: json['time'] ?? '',
      owner: User.fromJson(json['expand']?['owner'] ?? {}),
      guests: (json['expand']?['guests'] as List<dynamic>?)
              ?.map((guest) => User.fromJson(guest))
              .toList() ??
          [],
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'privacy': privacy.toString(),
      'region': region,
      'building': building,
      'datetime': datetime.toIso8601String(),
      'time': time,
      'owner': owner.id,
      'guests': guests.map((guest) => guest.id).toList(),
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
    };
  }

  Appointment copyWith({
    String? id,
    String? title,
    AppointmentPrivacy? privacy,
    String? region,
    String? building,
    DateTime? datetime,
    String? time,
    User? owner,
    List<User>? guests,
    DateTime? created,
    DateTime? updated,
  }) {
    return Appointment(
      id: id ?? this.id,
      title: title ?? this.title,
      privacy: privacy ?? this.privacy,
      region: region ?? this.region,
      building: building ?? this.building,
      datetime: datetime ?? this.datetime,
      time: time ?? this.time,
      owner: owner ?? this.owner,
      guests: guests ?? this.guests,
      created: created ?? this.created,
      updated: updated ?? this.updated,
    );
  }

  // Helper methods
  bool get isPublic => privacy == AppointmentPrivacy.public;
  bool get isPrivate => privacy == AppointmentPrivacy.private;
  
  bool isOwner(String userId) => owner.id == userId;
  bool isGuest(String userId) => guests.any((guest) => guest.id == userId);
  bool isParticipant(String userId) => isOwner(userId) || isGuest(userId);

  String get formattedDateTime {
    return '${datetime.day}/${datetime.month}/${datetime.year} - $time';
  }

  String get location {
    if (building != null && building!.isNotEmpty) {
      return '$region - $building';
    }
    return region;
  }

  @override
  String toString() {
    return 'Appointment(id: $id, title: $title, datetime: $datetime, owner: ${owner.username})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Appointment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum AppointmentPrivacy {
  public,
  private;

  static AppointmentPrivacy fromString(String privacy) {
    switch (privacy.toLowerCase()) {
      case 'public':
        return AppointmentPrivacy.public;
      case 'private':
        return AppointmentPrivacy.private;
      default:
        return AppointmentPrivacy.private;
    }
  }

  @override
  String toString() {
    return name;
  }

  String get displayName {
    switch (this) {
      case AppointmentPrivacy.public:
        return 'عام';
      case AppointmentPrivacy.private:
        return 'خاص';
    }
  }
}
