/// <reference path="../pb_data/types.d.ts" />
migrate((app) => {
  const collection = app.findCollectionByNameOrId("pbc_1037645436")

  // add field
  collection.fields.addAt(15, new Field({
    "hidden": false,
    "id": "select3729314941",
    "maxSelect": 1,
    "name": "calendar_type",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "select",
    "values": [
      "gregorian",
      "hijri"
    ]
  }))

  return app.save(collection)
}, (app) => {
  const collection = app.findCollectionByNameOrId("pbc_1037645436")

  // remove field
  collection.fields.removeById("select3729314941")

  return app.save(collection)
})
