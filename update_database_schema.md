# تحديث قاعدة البيانات لتطبيق سجلي

## 🎯 المطلوب تحديثه حسب المخطط النهائي:

### 1. جدول المستخدمين (users) - موجود ✅
```
- id (text, primary key)
- username (text, unique)
- email (text, unique)
- password (text)
- role (select: user, approved, admin)
- created (datetime)
- updated (datetime)
```

### 2. جدول البروفايلات (profiles) - يحتاج تحديث ⚠️
```
الحقول المطلوبة:
- id (text, primary key)
- user (relation to users, unique)
- avatar (file, optional)
- bio (text, optional, max 500)
- instagram (text, optional)
- youtube (text, optional)
- created (datetime)
- updated (datetime)
```

### 3. جدول المواعيد (appointments) - موجود ✅
```
- id (text, primary key)
- title (text)
- privacy (select: public, private)
- region (text)
- building (text, optional)
- datetime (datetime)
- time (text)
- owner (relation to users)
- guests (relation to users, multiple)
- status (select: active, cancelled, completed)
- created (datetime)
- updated (datetime)
```

### 4. جدول المتابعة (friendships) - جديد ➕
```
- id (text, primary key)
- follower (relation to users)
- following (relation to users)
- status (select: pending, accepted, blocked)
- created (datetime)
- updated (datetime)
```

### 5. جدول الإشعارات (inbox) - جديد ➕
```
- id (text, primary key)
- recipient (relation to users)
- sender (relation to users)
- type (select: invitation, accept, decline, delete, follow)
- appointment (relation to appointments, optional)
- message (text, optional)
- read (bool, default false)
- created (datetime)
- updated (datetime)
```

## 🔧 خطوات التحديث في Pocketbase:

### الخطوة 1: تحديث جدول profiles
1. اذهب إلى Collections → profiles
2. احذف الحقول غير المطلوبة:
   - display_name
   - phone
   - location
   - birth_date
   - gender
   - website
   - social_links
   - privacy_settings
   - notification_settings
   - is_verified
   - is_public

3. أضف الحقول الجديدة:
   - **avatar** (File): Max 1 file, 5MB, images only
   - **bio** (Text): Max 500 characters
   - **instagram** (Text): Max 100 characters
   - **youtube** (Text): Max 100 characters

### الخطوة 2: إنشاء جدول friendships
1. اضغط "New collection" → "Base collection"
2. اسم الجدول: `friendships`
3. أضف الحقول:
   - **follower** (Relation): users collection, required
   - **following** (Relation): users collection, required
   - **status** (Select): pending, accepted, blocked

### الخطوة 3: إنشاء جدول inbox
1. اضغط "New collection" → "Base collection"
2. اسم الجدول: `inbox`
3. أضف الحقول:
   - **recipient** (Relation): users collection, required
   - **sender** (Relation): users collection, required
   - **type** (Select): invitation, accept, decline, delete, follow
   - **appointment** (Relation): appointments collection, optional
   - **message** (Text): Max 500 characters, optional
   - **read** (Bool): Default false

### الخطوة 4: تحديث قواعد الوصول

#### profiles:
```
List: @request.auth.id != ""
View: @request.auth.id != ""
Create: @request.auth.id != "" && user.id = @request.auth.id
Update: @request.auth.id != "" && user.id = @request.auth.id
Delete: @request.auth.id != "" && user.id = @request.auth.id
```

#### friendships:
```
List: @request.auth.id != "" && (follower.id = @request.auth.id || following.id = @request.auth.id)
View: @request.auth.id != "" && (follower.id = @request.auth.id || following.id = @request.auth.id)
Create: @request.auth.id != "" && follower.id = @request.auth.id
Update: @request.auth.id != "" && (follower.id = @request.auth.id || following.id = @request.auth.id)
Delete: @request.auth.id != "" && follower.id = @request.auth.id
```

#### inbox:
```
List: @request.auth.id != "" && recipient.id = @request.auth.id
View: @request.auth.id != "" && recipient.id = @request.auth.id
Create: @request.auth.id != "" && sender.id = @request.auth.id
Update: @request.auth.id != "" && recipient.id = @request.auth.id
Delete: @request.auth.id != "" && recipient.id = @request.auth.id
```

## ✅ بعد التحديث:
- جرب التطبيق المحدث
- تأكد من عمل البروفايل الجديد
- اختبر إضافة النبذة الشخصية وروابط التواصل
- تحقق من عمل البحث

## 🚀 الميزات الجديدة المتاحة:
- ✅ نبذة شخصية
- ✅ روابط Instagram و YouTube
- ✅ رفع صورة شخصية (avatar)
- 🔄 نظام المتابعة (قريباً)
- 🔄 نظام الإشعارات (قريباً)
