<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sijilli - متصل مع Pocketbase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }

        .app-container {
            max-width: 360px;
            margin: 0 auto;
            background: #ffffff;
            min-height: 100vh;
            box-shadow: 0 0 15px rgba(0,0,0,0.08);
            position: relative;
        }

        /* Login Screen */
        .login-screen {
            padding: 40px 24px;
            text-align: center;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .app-logo {
            font-size: 28px;
            color: #495057;
            font-weight: 600;
            margin-bottom: 6px;
        }

        .login-subtitle {
            color: #6c757d;
            margin-bottom: 40px;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 16px;
            text-align: right;
        }

        .form-input {
            width: 100%;
            padding: 10px 14px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            font-size: 14px;
            direction: ltr;
            text-align: left;
            background-color: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #495057;
            background-color: #ffffff;
        }

        .login-btn {
            width: 100%;
            padding: 10px;
            background: #495057;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin-bottom: 14px;
            font-weight: 500;
        }

        .login-btn:hover {
            background: #343a40;
        }

        .login-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .register-link {
            color: #495057;
            text-decoration: none;
            font-size: 13px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 16px;
            font-size: 12px;
            border: 1px solid #f5c6cb;
        }

        /* Main App */
        .main-app {
            display: none;
        }

        .app-bar {
            background: #495057;
            color: white;
            padding: 12px;
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            position: relative;
        }

        .app-bar .refresh-btn {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            opacity: 0.8;
        }

        .app-bar .refresh-btn:hover {
            opacity: 1;
        }

        .content {
            padding-bottom: 80px;
            min-height: calc(100vh - 140px);
        }

        .appointment-card {
            margin: 12px;
            background: #ffffff;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            box-shadow: 0 1px 3px rgba(0,0,0,0.06);
            transition: all 0.2s ease;
        }

        .appointment-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            border-color: #adb5bd;
        }

        .card-header {
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .owner-info {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .avatar {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            overflow: hidden;
            border: 1px solid #dee2e6;
        }

        .avatar-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .avatar-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
            background: #6c757d;
        }

        .owner-name {
            font-size: 10px;
            font-weight: 500;
            color: #495057;
        }

        .days-remaining {
            font-size: 9px;
            color: #6c757d;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 8px;
            white-space: nowrap;
            font-weight: 500;
        }

        /* ألوان مختلفة حسب قرب الموعد */
        .days-remaining.today {
            background: #d4edda;
            color: #155724;
        }

        .days-remaining.tomorrow {
            background: #fff3cd;
            color: #856404;
        }

        .days-remaining.day-after-tomorrow {
            background: #cce5ff;
            color: #004085;
        }

        .card-bottom {
            padding: 10px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .copy-btn {
            padding: 4px 8px;
            border: 1px solid #adb5bd;
            border-radius: 4px;
            background: #f8f9fa;
            font-size: 9px;
            cursor: pointer;
            color: #495057;
            font-weight: 500;
        }

        .copy-btn:hover {
            background: #e9ecef;
            border-color: #6c757d;
        }

        .appointment-details {
            text-align: center;
            flex: 1;
            margin: 0 8px;
            direction: rtl;
        }

        .appointment-title {
            font-size: 12px;
            font-weight: 600;
            color: #212529;
            margin-bottom: 3px;
        }

        .location {
            font-size: 10px;
            font-weight: 500;
            color: #495057;
            margin-bottom: 2px;
        }

        .datetime {
            font-size: 9px;
            color: #6c757d;
        }

        .guest-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            position: relative;
            overflow: hidden;
            border: 1px solid #dee2e6;
        }

        .guest-avatar .avatar-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .guest-avatar .avatar-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            background: #6c757d;
        }

        .status-indicator {
            position: absolute;
            top: 0;
            right: 0;
            width: 8px;
            height: 8px;
            background: #dc3545;
            border-radius: 50%;
            border: 1px solid white;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 360px;
            background: #ffffff;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: space-around;
            padding: 6px 0;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 6px;
            color: #6c757d;
            text-decoration: none;
            font-size: 10px;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .nav-item.active {
            color: #495057;
        }

        .nav-icon {
            margin-bottom: 2px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .nav-icon svg {
            transition: all 0.2s ease;
        }

        .loading {
            text-align: center;
            padding: 30px;
            color: #6c757d;
            font-size: 12px;
        }

        .empty-state {
            text-align: center;
            padding: 30px;
            color: #6c757d;
            font-size: 12px;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 12px;
        }

        .fab {
            position: fixed;
            bottom: 75px;
            left: 50%;
            transform: translateX(-50%);
            margin-left: 130px;
            width: 48px;
            height: 48px;
            background: #495057;
            border-radius: 50%;
            border: none;
            color: white;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            transition: all 0.2s ease;
        }

        .fab:hover {
            background: #343a40;
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .connection-status {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 8px 12px;
            margin: 12px;
            border-radius: 4px;
            text-align: center;
            font-size: 11px;
            color: #0c5460;
        }

        .connection-status.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .connection-status.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        /* Profile Screen */
        .profile-screen {
            display: none;
            padding: 20px;
        }

        .profile-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: #495057;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            margin: 0 auto 12px;
            position: relative;
            cursor: pointer;
        }

        .profile-avatar:hover {
            background: #343a40;
        }

        .profile-name {
            font-size: 18px;
            font-weight: 600;
            color: #212529;
            margin-bottom: 4px;
        }

        .profile-username {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 8px;
        }

        .profile-email {
            font-size: 12px;
            color: #6c757d;
        }

        .profile-section {
            margin-bottom: 24px;
        }

        .profile-section-title {
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 12px;
            padding-bottom: 6px;
            border-bottom: 1px solid #dee2e6;
        }

        .profile-field {
            margin-bottom: 16px;
        }

        .profile-field-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .profile-field-value {
            font-size: 14px;
            color: #212529;
            padding: 8px 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            min-height: 36px;
            display: flex;
            align-items: center;
        }

        .profile-field-value.empty {
            color: #6c757d;
            font-style: italic;
        }

        .profile-field-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 14px;
            background: #ffffff;
        }

        .profile-field-input:focus {
            outline: none;
            border-color: #495057;
        }

        .profile-field-input[type="textarea"],
        textarea.profile-field-input {
            resize: vertical;
            min-height: 60px;
            font-family: inherit;
        }

        /* Add Appointment Form Styles */
        .add-form {
            padding: 0 16px 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 12px;
            font-weight: 500;
            color: #495057;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            font-size: 14px;
            background: #fff;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #495057;
            box-shadow: 0 0 0 2px rgba(73, 80, 87, 0.1);
        }

        .privacy-options {
            display: flex;
            gap: 12px;
        }

        .privacy-option {
            flex: 1;
            cursor: pointer;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.2s ease;
        }

        .privacy-option.selected {
            border-color: #495057;
            background: #f8f9fa;
        }

        .privacy-option input[type="radio"] {
            display: none;
        }

        .privacy-content {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .privacy-icon {
            font-size: 16px;
        }

        .privacy-text {
            flex: 1;
        }

        .privacy-title {
            font-size: 12px;
            font-weight: 500;
            color: #495057;
        }

        .privacy-desc {
            font-size: 10px;
            color: #6c757d;
        }

        .guests-section {
            position: relative;
        }

        .guest-search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #fff;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 6px 6px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 10;
            display: none;
        }

        .guest-search-results.show {
            display: block;
        }

        .guest-result {
            padding: 8px 12px;
            border-bottom: 1px solid #f1f3f4;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .guest-result:hover {
            background: #f8f9fa;
        }

        .guest-result:last-child {
            border-bottom: none;
        }

        .selected-guests {
            margin-top: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .guest-tag {
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .guest-tag .remove-guest {
            cursor: pointer;
            color: #6c757d;
            font-weight: bold;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }

        .btn-primary, .btn-secondary {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #495057;
            color: #fff;
        }

        .btn-primary:hover {
            background: #343a40;
        }

        .btn-primary:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        /* Add Screen Specific Styles */
        #addScreen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #fff;
            z-index: 1000;
            display: flex;
            flex-direction: column;
        }

        #addScreen .screen-header {
            background: #fff;
            border-bottom: 1px solid #dee2e6;
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            flex-shrink: 0;
            position: relative;
        }

        #addScreen .screen-header h2 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #495057;
        }

        #addScreen .back-btn {
            position: static;
            transform: none;
            background: none;
            border: none;
            color: #495057;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
        }

        #addScreen .back-btn:hover {
            background: #f8f9fa;
        }

        #addScreen .screen-content {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 80px; /* مساحة للنافيجيتور */
        }

        /* Avatar Section Styles */
        .avatar-section {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .avatar-display {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fff;
        }

        .avatar-display img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .avatar-placeholder {
            font-size: 32px;
            color: #6c757d;
        }

        .avatar-controls {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .avatar-btn {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background: #fff;
            color: #495057;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .avatar-btn:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }

        .avatar-btn-remove {
            background: #dc3545;
            color: #fff;
            border-color: #dc3545;
        }

        .avatar-btn-remove:hover {
            background: #c82333;
            border-color: #bd2130;
        }

        /* Register Screen Styles */
        .register-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .register-step {
            width: 100%;
            max-width: 400px;
        }

        .step-indicator {
            margin-bottom: 30px;
            text-align: center;
        }

        .step-title {
            font-size: 14px;
            margin-bottom: 12px;
            opacity: 0.9;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #fff;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .input-hint {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 4px;
            margin-right: 8px;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        .form-actions .login-btn {
            flex: 1;
        }

        .login-btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .login-btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .profile-field-input.valid {
            border-color: #28a745;
        }

        .profile-field-input.invalid {
            border-color: #dc3545;
        }

        .username-hint {
            font-size: 10px;
            color: #6c757d;
            margin-top: 4px;
            font-style: italic;
        }

        .username-example {
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 10px;
            color: #495057;
            margin-top: 4px;
        }

        .profile-actions {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        .profile-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .profile-btn-primary {
            background: #495057;
            color: white;
        }

        .profile-btn-primary:hover {
            background: #343a40;
        }

        .profile-btn-secondary {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .profile-btn-secondary:hover {
            background: #e9ecef;
        }

        .profile-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .profile-stats {
            display: flex;
            justify-content: space-around;
            padding: 16px 0;
            margin: 16px 0;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .profile-stat {
            text-align: center;
        }

        .profile-stat-number {
            font-size: 18px;
            font-weight: 600;
            color: #495057;
        }

        .profile-stat-label {
            font-size: 10px;
            color: #6c757d;
            margin-top: 2px;
        }

        .back-btn {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            opacity: 0.8;
        }

        .back-btn:hover {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Login Screen -->
        <div class="login-screen" id="loginScreen">
            <div class="app-logo">سِجِلّي</div>
            <div class="login-subtitle">تسجيل الدخول - متصل مع Pocketbase</div>
            
            <div id="errorMessage" class="error-message" style="display: none;"></div>
            
            <div class="form-group">
                <input type="email" class="form-input" placeholder="البريد الإلكتروني" id="emailInput" value="<EMAIL>" onkeypress="handleLoginKeyPress(event)">
            </div>

            <div class="form-group">
                <input type="password" class="form-input" placeholder="كلمة المرور" id="passwordInput" value="123456789" onkeypress="handleLoginKeyPress(event)">
            </div>
            
            <button class="login-btn" onclick="safeLogin()" id="loginBtn">تسجيل الدخول</button>

            <a href="#" class="register-link" onclick="safeShowRegister()">ليس لديك حساب؟ إنشاء حساب جديد</a>
        </div>

        <!-- Register Screen -->
        <div class="register-screen" id="registerScreen" style="display: none;">
            <div class="app-logo">سِجِلّي</div>
            <div class="login-subtitle">إنشاء حساب جديد - متصل مع Pocketbase</div>

            <div id="registerErrorMessage" class="error-message" style="display: none;"></div>

            <!-- الخطوة 1: المعلومات الأساسية -->
            <div id="registerStep1" class="register-step">
                <div class="step-indicator">
                    <div class="step-title">الخطوة 1 من 2: المعلومات الأساسية</div>
                    <div class="step-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 50%;"></div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <input type="text" class="form-input" placeholder="اسم المستخدم (حروف إنجليزية فقط)"
                           id="registerUsername" pattern="[a-zA-Z0-9._]+"
                           title="حروف إنجليزية وأرقام ونقطة وأندرسكور فقط">
                    <div class="input-hint">مثال: ahmed.ali، user_123</div>
                </div>

                <div class="form-group">
                    <input type="email" class="form-input" placeholder="البريد الإلكتروني" id="registerEmail">
                </div>

                <div class="form-group">
                    <input type="password" class="form-input" placeholder="كلمة المرور (8 أحرف على الأقل)"
                           id="registerPassword" minlength="8">
                </div>

                <div class="form-group">
                    <input type="password" class="form-input" placeholder="تأكيد كلمة المرور"
                           id="registerPasswordConfirm">
                </div>

                <button class="login-btn" onclick="nextStep()" id="nextStepBtn">التالي</button>
                <a href="#" class="register-link" onclick="showLogin()">لديك حساب بالفعل؟ تسجيل الدخول</a>
            </div>

            <!-- الخطوة 2: المعلومات الشخصية -->
            <div id="registerStep2" class="register-step" style="display: none;">
                <div class="step-indicator">
                    <div class="step-title">الخطوة 2 من 2: المعلومات الشخصية (اختيارية)</div>
                    <div class="step-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%;"></div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <input type="text" class="form-input" placeholder="اسم العرض (كما تريد أن يظهر للآخرين)"
                           id="registerDisplayName" maxlength="50">
                    <div class="input-hint">مثال: أحمد علي، فاطمة محمد</div>
                </div>

                <div class="form-group">
                    <input type="text" class="form-input" placeholder="المهنة أو التخصص"
                           id="registerJob" maxlength="100">
                    <div class="input-hint">مثال: مطور برمجيات، مصمم، طبيب</div>
                </div>

                <div class="form-group">
                    <input type="tel" class="form-input" placeholder="رقم الهاتف"
                           id="registerPhone" maxlength="20">
                    <div class="input-hint">مثال: +965-12345678</div>
                </div>

                <div class="form-group">
                    <textarea class="form-input" placeholder="نبذة شخصية (اختيارية)"
                              id="registerBio" maxlength="500" rows="3"></textarea>
                    <div class="input-hint">اكتب نبذة مختصرة عن نفسك</div>
                </div>

                <div class="form-actions">
                    <button class="login-btn login-btn-secondary" onclick="prevStep()">السابق</button>
                    <button class="login-btn" onclick="register()" id="registerBtn">إنشاء الحساب</button>
                </div>
            </div>
        </div>

        <!-- Main App -->
        <div class="main-app" id="mainApp">
            <div class="app-bar">
                <button class="refresh-btn" onclick="refreshAppointments()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="23,4 23,10 17,10"/>
                        <polyline points="1,20 1,14 7,14"/>
                        <path d="m20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4-4.64 4.36A9 9 0 0 1 3.51 15"/>
                    </svg>
                </button>
                <span id="appBarTitle">مواعيدي</span>
            </div>

            <div class="content" id="content">
                <div id="connectionStatus" class="connection-status">
                    🔗 متصل مع Pocketbase على http://127.0.0.1:8090
                </div>

                <div id="appointmentsList">
                    <!-- Appointments will be loaded here -->
                </div>

                <!-- Add Appointment Screen -->
                <div id="addScreen" class="screen" style="display: none;">
                    <div class="screen-header">
                        <button class="back-btn" onclick="closeAdd()">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="m15 18-6-6 6-6"/>
                            </svg>
                        </button>
                        <h2>إضافة موعد جديد</h2>
                    </div>
                    <div class="screen-content">
                        <form id="addAppointmentForm" class="add-form">
                            <!-- الموضوع -->
                            <div class="form-group">
                                <label class="form-label">الموضوع *</label>
                                <input type="text" id="appointmentTitle" class="form-input"
                                       placeholder="مثال: اجتماع العمل، عشاء العائلة"
                                       required maxlength="100">
                            </div>

                            <!-- الخصوصية -->
                            <div class="form-group">
                                <label class="form-label">الخصوصية *</label>
                                <div class="privacy-options">
                                    <label class="privacy-option selected">
                                        <input type="radio" name="privacy" value="open" checked>
                                        <div class="privacy-content">
                                            <div class="privacy-icon">🌍</div>
                                            <div class="privacy-text">
                                                <div class="privacy-title">مفتوح</div>
                                                <div class="privacy-desc">يمكن للجميع رؤيته</div>
                                            </div>
                                        </div>
                                    </label>
                                    <label class="privacy-option">
                                        <input type="radio" name="privacy" value="private">
                                        <div class="privacy-content">
                                            <div class="privacy-icon">🔒</div>
                                            <div class="privacy-text">
                                                <div class="privacy-title">خاص</div>
                                                <div class="privacy-desc">للمدعوين فقط</div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- المنطقة -->
                            <div class="form-group">
                                <label class="form-label">المنطقة *</label>
                                <input type="text" id="appointmentRegion" class="form-input"
                                       placeholder="مثال: الكويت، حولي، الجهراء"
                                       required maxlength="50">
                            </div>

                            <!-- المبنى -->
                            <div class="form-group">
                                <label class="form-label">المبنى</label>
                                <input type="text" id="appointmentBuilding" class="form-input"
                                       placeholder="مثال: برج التجارة، مجمع الأفنيوز (اختياري)"
                                       maxlength="50">
                            </div>

                            <!-- التاريخ -->
                            <div class="form-group">
                                <label class="form-label">التاريخ *</label>
                                <input type="date" id="appointmentDate" class="form-input" required>
                            </div>

                            <!-- الوقت -->
                            <div class="form-group">
                                <label class="form-label">الوقت *</label>
                                <input type="time" id="appointmentTime" class="form-input" required>
                            </div>

                            <!-- المدعوين -->
                            <div class="form-group">
                                <label class="form-label">المدعوين</label>
                                <div class="guests-section">
                                    <input type="text" id="guestSearch" class="form-input"
                                           placeholder="ابحث عن مستخدم لدعوته..."
                                           onkeyup="searchGuests(this.value)">
                                    <div id="guestSearchResults" class="guest-search-results"></div>
                                    <div id="selectedGuests" class="selected-guests"></div>
                                </div>
                            </div>

                            <!-- أزرار الحفظ -->
                            <div class="form-actions">
                                <button type="button" class="btn-secondary" onclick="closeAdd()">إلغاء</button>
                                <button type="submit" class="btn-primary" id="saveAppointmentBtn">إنشاء الموعد</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Floating Action Button -->
            <button class="fab" onclick="addAppointment()">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="5" x2="12" y2="19"/>
                    <line x1="5" y1="12" x2="19" y2="12"/>
                </svg>
            </button>

            <!-- Bottom Navigation -->
            <div class="bottom-nav">
                <div class="nav-item active" onclick="showHome()">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                            <polyline points="9,22 9,12 15,12 15,22"/>
                        </svg>
                    </div>
                    <div>الرئيسية</div>
                </div>
                <div class="nav-item" onclick="showSearch()">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="m21 21-4.35-4.35"/>
                        </svg>
                    </div>
                    <div>البحث</div>
                </div>
                <div class="nav-item" onclick="addAppointment()">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <line x1="12" y1="8" x2="12" y2="16"/>
                            <line x1="8" y1="12" x2="16" y2="12"/>
                        </svg>
                    </div>
                    <div>إضافة</div>
                </div>
                <div class="nav-item" onclick="showNotifications()">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"/>
                            <path d="m13.73 21a2 2 0 0 1-3.46 0"/>
                        </svg>
                    </div>
                    <div>الإشعارات</div>
                </div>
                <div class="nav-item" onclick="showSettings()">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="m12 1 2.09 6.26L22 9l-5.91 2.09L14 17l-2.09-5.91L4 9l5.91-2.09L12 1z"/>
                        </svg>
                    </div>
                    <div>الإعدادات</div>
                </div>
            </div>


        </div>

        <!-- Profile Screen -->
        <div class="profile-screen" id="profileScreen">
            <div class="app-bar">
                <button class="back-btn" onclick="closeProfile()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="15,18 9,12 15,6"/>
                    </svg>
                </button>
                <span>الملف الشخصي</span>
            </div>

            <div class="profile-header">
                <div class="profile-avatar" onclick="changeAvatar()">
                    <span id="profileAvatarText">👤</span>
                </div>
                <div class="profile-name" id="profileDisplayName">اسم المستخدم</div>
                <div class="profile-username" id="profileUsername">@username</div>
                <div class="profile-email" id="profileEmail"><EMAIL></div>

                <div class="profile-stats">
                    <div class="profile-stat">
                        <div class="profile-stat-number" id="appointmentsCount">0</div>
                        <div class="profile-stat-label">المواعيد</div>
                    </div>
                    <div class="profile-stat">
                        <div class="profile-stat-number" id="invitationsCount">0</div>
                        <div class="profile-stat-label">الدعوات</div>
                    </div>
                    <div class="profile-stat">
                        <div class="profile-stat-number" id="joinedDate">2024</div>
                        <div class="profile-stat-label">انضم في</div>
                    </div>
                </div>
            </div>

            <div class="profile-section">
                <div class="profile-section-title">الصورة الشخصية</div>

                <div class="profile-field">
                    <div class="profile-field-label">الصورة الشخصية</div>
                    <div class="avatar-section">
                        <div class="avatar-display" id="avatarDisplay">
                            <img id="avatarImage" src="" alt="الصورة الشخصية" style="display: none;">
                            <div id="avatarPlaceholder" class="avatar-placeholder">📷</div>
                        </div>
                        <div class="avatar-controls">
                            <input type="file" id="avatarInput" accept="image/*" style="display: none;" onchange="handleAvatarChange(event)">
                            <button type="button" class="avatar-btn" onclick="document.getElementById('avatarInput').click()">
                                <span id="avatarBtnText">اختيار صورة</span>
                            </button>
                            <button type="button" class="avatar-btn avatar-btn-remove" id="removeAvatarBtn" onclick="removeAvatar()" style="display: none;">
                                حذف الصورة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="profile-section">
                <div class="profile-section-title">المعلومات الأساسية</div>

                <div class="profile-field">
                    <div class="profile-field-label">اسم العرض</div>
                    <div class="profile-field-value" id="displayNameView">لم يتم تعيين اسم عرض</div>
                    <input type="text" class="profile-field-input" id="displayNameEdit" style="display: none;"
                           placeholder="اسمك كما تريد أن يظهر للآخرين"
                           maxlength="50">
                </div>

                <div class="profile-field">
                    <div class="profile-field-label">اسم المستخدم (حروف إنجليزية، نقطة، أندرسكور فقط)</div>
                    <div class="profile-field-value" id="usernameView">username</div>
                    <input type="text" class="profile-field-input" id="usernameEdit" style="display: none;"
                           placeholder="username_example.123"
                           pattern="[a-zA-Z0-9._]+"
                           title="حروف إنجليزية وأرقام ونقطة وأندرسكور فقط">
                    <div class="username-hint" id="usernameHint" style="display: none;">
                        ✓ يمكن استخدام: حروف إنجليزية (a-z, A-Z)، أرقام (0-9)، نقطة (.)، أندرسكور (_)<br>
                        ✗ لا يمكن البدء أو الانتهاء بنقطة أو أندرسكور<br>
                        ✗ لا يمكن استخدام رموز متتالية (.., __, ._, _.)
                    </div>
                    <div class="username-example" id="usernameExample" style="display: none;">
                        أمثلة صحيحة: ahmed.ali، user_123، my.username_2024
                    </div>
                </div>

                <div class="profile-field">
                    <div class="profile-field-label">البريد الإلكتروني</div>
                    <div class="profile-field-value" id="emailView"><EMAIL></div>
                    <input type="email" class="profile-field-input" id="emailEdit" style="display: none;">
                </div>

                <div class="profile-field">
                    <div class="profile-field-label">الحرفة / المهنة</div>
                    <div class="profile-field-value" id="jobView">لم يتم تحديد المهنة</div>
                    <input type="text" class="profile-field-input" id="jobEdit" style="display: none;"
                           placeholder="مثال: مطور برمجيات، مصمم، طبيب"
                           maxlength="100">
                </div>

                <div class="profile-field">
                    <div class="profile-field-label">رقم الهاتف</div>
                    <div class="profile-field-value" id="phoneView">لم يتم إضافة رقم هاتف</div>
                    <input type="tel" class="profile-field-input" id="phoneEdit" style="display: none;"
                           placeholder="مثال: +965-12345678"
                           pattern="[+]?[0-9\-\s()]*">
                </div>

                <div class="profile-field">
                    <div class="profile-field-label">النبذة الشخصية</div>
                    <div class="profile-field-value" id="bioView">لم يتم إضافة نبذة شخصية</div>
                    <textarea class="profile-field-input" id="bioEdit" style="display: none;"
                              placeholder="اكتب نبذة عن نفسك..."
                              maxlength="500" rows="3"></textarea>
                </div>
            </div>

            <div class="profile-section">
                <div class="profile-section-title">روابط التواصل الاجتماعي</div>

                <div class="profile-field">
                    <div class="profile-field-label">Instagram</div>
                    <div class="profile-field-value" id="instagramView">لم يتم إضافة رابط</div>
                    <input type="text" class="profile-field-input" id="instagramEdit" style="display: none;"
                           placeholder="اسم المستخدم في Instagram">
                </div>

                <div class="profile-field">
                    <div class="profile-field-label">YouTube</div>
                    <div class="profile-field-value" id="youtubeView">لم يتم إضافة رابط</div>
                    <input type="text" class="profile-field-input" id="youtubeEdit" style="display: none;"
                           placeholder="اسم القناة في YouTube">
                </div>
            </div>

            <div class="profile-section">
                <div class="profile-section-title">معلومات إضافية</div>

                <div class="profile-field">
                    <div class="profile-field-label">تاريخ الانضمام</div>
                    <div class="profile-field-value" id="createdDate">-</div>
                </div>

                <div class="profile-field">
                    <div class="profile-field-label">آخر تحديث</div>
                    <div class="profile-field-value" id="updatedDate">-</div>
                </div>

                <div class="profile-field">
                    <div class="profile-field-label">حالة التحقق</div>
                    <div class="profile-field-value" id="verifiedStatus">
                        <span style="color: #28a745;">✓ محقق</span>
                    </div>
                </div>
            </div>

            <div class="profile-actions">
                <button class="profile-btn profile-btn-primary" id="editBtn" onclick="toggleEdit()">تعديل</button>
                <button class="profile-btn profile-btn-secondary" id="cancelBtn" onclick="cancelEdit()" style="display: none;">إلغاء</button>
                <button class="profile-btn profile-btn-primary" id="saveBtn" onclick="saveProfile()" style="display: none;">حفظ والعودة</button>
            </div>
        </div>
    </div>

    <script>
        // Error handling
        window.onerror = function(msg, url, lineNo, columnNo, error) {
            console.error('JavaScript Error:', {
                message: msg,
                source: url,
                line: lineNo,
                column: columnNo,
                error: error
            });
            return false;
        };

        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled Promise Rejection:', event.reason);
        });

        // Test if JavaScript is working
        console.log('JavaScript loaded successfully');

        // Pocketbase Configuration
        const POCKETBASE_URL = 'http://127.0.0.1:8090';
        let pb = null;
        let currentUser = null;
        let currentTab = 'home';
        let selectedGuests = [];

        // Test functions
        function testLogin() {
            console.log('Test login function called');
            alert('Login button is working! Check console for details.');
        }

        function testRegister() {
            console.log('Test register function called');
            alert('Register link is working! Check console for details.');
        }

        // Safe wrapper functions
        function safeLogin() {
            try {
                console.log('Safe login called');
                if (typeof login === 'function') {
                    login();
                } else {
                    console.error('Login function not found');
                    alert('خطأ: دالة تسجيل الدخول غير موجودة');
                }
            } catch (error) {
                console.error('Error in safeLogin:', error);
                alert('خطأ في تسجيل الدخول: ' + error.message);
            }
        }

        function safeShowRegister() {
            try {
                console.log('Safe show register called');
                if (typeof showRegister === 'function') {
                    showRegister();
                } else {
                    console.error('ShowRegister function not found');
                    alert('خطأ: دالة التسجيل غير موجودة');
                }
            } catch (error) {
                console.error('Error in safeShowRegister:', error);
                alert('خطأ في عرض التسجيل: ' + error.message);
            }
        }

        // Initialize Pocketbase
        async function initPocketbase() {
            try {
                // Import PocketBase from CDN
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/pocketbase@0.21.3/dist/pocketbase.umd.js';
                document.head.appendChild(script);

                script.onload = () => {
                    console.log('PocketBase script loaded, initializing...');
                    pb = new PocketBase(POCKETBASE_URL);
                    console.log('PocketBase initialized:', pb);
                    console.log('PocketBase URL:', POCKETBASE_URL);
                    checkConnection();
                };
            } catch (error) {
                console.error('Failed to initialize PocketBase:', error);
                showConnectionStatus('خطأ في الاتصال مع Pocketbase', 'error');
            }
        }

        // Check connection to Pocketbase
        async function checkConnection() {
            console.log('Checking PocketBase connection...');
            try {
                await pb.health.check();
                console.log('PocketBase health check passed');
                showConnectionStatus('✅ متصل مع Pocketbase بنجاح', 'success');

                // Check if user is already authenticated
                if (pb.authStore.isValid) {
                    console.log('User already authenticated:', pb.authStore.model);
                    currentUser = pb.authStore.model;
                    showMainApp();
                } else {
                    console.log('No authenticated user found');
                }
            } catch (error) {
                console.error('Connection failed:', error);
                showConnectionStatus('❌ فشل الاتصال مع Pocketbase - تأكد من تشغيل الخادم', 'error');
            }
        }

        // Show connection status
        function showConnectionStatus(message, type = 'info') {
            const statusEl = document.getElementById('connectionStatus');
            if (statusEl) {
                statusEl.textContent = message;
                statusEl.className = `connection-status ${type}`;
            }
        }

        // Handle Enter key press in login form
        function handleLoginKeyPress(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                login();
            }
        }

        // Login function
        async function login() {
            console.log('Login function called');

            const email = document.getElementById('emailInput').value;
            const password = document.getElementById('passwordInput').value;
            const loginBtn = document.getElementById('loginBtn');
            const errorEl = document.getElementById('errorMessage');

            console.log('Login attempt:', { email, password: password ? '***' : 'empty' });

            if (!email || !password) {
                showError('يرجى إدخال البريد الإلكتروني وكلمة المرور');
                return;
            }

            if (!pb) {
                console.error('PocketBase not initialized');
                showError('لم يتم الاتصال مع Pocketbase بعد. يرجى إعادة تحميل الصفحة');
                return;
            }

            console.log('PocketBase instance:', pb);

            loginBtn.disabled = true;
            loginBtn.textContent = 'جاري تسجيل الدخول...';
            errorEl.style.display = 'none';

            try {
                console.log('Attempting authentication...');
                const authData = await pb.collection('users').authWithPassword(email, password);
                console.log('Authentication successful:', authData);

                currentUser = authData.record;
                console.log('Current user set:', currentUser);

                showMainApp();
                showNotification('تم تسجيل الدخول بنجاح!', 'success');
            } catch (error) {
                console.error('Login failed:', error);
                console.error('Error details:', error.response || error);

                let errorMessage = 'خطأ غير معروف';
                if (error.status === 400) {
                    errorMessage = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
                } else if (error.message) {
                    errorMessage = error.message;
                }

                showError('فشل تسجيل الدخول: ' + errorMessage);
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = 'تسجيل الدخول';
            }
        }

        // Show error message
        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
        }

        // Show main app
        function showMainApp() {
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('mainApp').style.display = 'block';
            loadAppointments();
        }

        // Load appointments from Pocketbase
        async function loadAppointments() {
            const appointmentsList = document.getElementById('appointmentsList');
            appointmentsList.innerHTML = '<div class="loading">جاري تحميل المواعيد من Pocketbase...</div>';

            try {
                if (!currentUser) {
                    throw new Error('المستخدم غير مسجل الدخول');
                }

                // Get appointments where user is owner or guest (new schema)
                // فلترة المواعيد المستقبلية فقط
                const now = new Date();
                const todayStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format

                const appointments = await pb.collection('appointments').getList(1, 50, {
                    filter: `(owner.id = "${currentUser.id}" || guests.id ?= "${currentUser.id}") && startDate >= "${todayStr}" && status = "active"`,
                    expand: 'owner,guests',
                    sort: '+startDate', // الأقرب زمنياً في الأعلى
                });

                // Load profiles for all users (owners and guests)
                const userIds = new Set();
                appointments.items.forEach(appointment => {
                    if (appointment.expand?.owner?.id) {
                        userIds.add(appointment.expand.owner.id);
                    }
                    if (appointment.expand?.guests) {
                        appointment.expand.guests.forEach(guest => {
                            if (guest.id) userIds.add(guest.id);
                        });
                    }
                });

                // Fetch profiles for all users
                const profiles = {};
                if (userIds.size > 0) {
                    try {
                        const userIdsArray = Array.from(userIds);
                        const profilesData = await pb.collection('profiles').getList(1, 100, {
                            filter: userIdsArray.map(id => `user.id = "${id}"`).join(' || '),
                            expand: 'user'
                        });

                        profilesData.items.forEach(profile => {
                            if (profile.expand?.user?.id) {
                                profiles[profile.expand.user.id] = profile;
                            }
                        });
                    } catch (profileError) {
                        console.warn('Could not load profiles:', profileError);
                    }
                }

                // Get profiles for all users in appointments
                const userIds = new Set();
                appointments.items.forEach(appointment => {
                    if (appointment.expand?.owner) {
                        userIds.add(appointment.expand.owner.id);
                    }
                    if (appointment.expand?.guests) {
                        appointment.expand.guests.forEach(guest => {
                            userIds.add(guest.id);
                        });
                    }
                });

                // Fetch profiles for all users
                const profiles = {};
                if (userIds.size > 0) {
                    try {
                        const userIdsArray = Array.from(userIds);
                        const profilesResult = await pb.collection('profiles').getList(1, 100, {
                            filter: userIdsArray.map(id => `user.id = "${id}"`).join(' || '),
                            expand: 'user'
                        });

                        profilesResult.items.forEach(profile => {
                            profiles[profile.expand.user.id] = profile;
                        });
                    } catch (profileError) {
                        console.warn('Could not load profiles:', profileError);
                    }
                }

                appointmentsList.innerHTML = '';

                if (appointments.items.length === 0) {
                    appointmentsList.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#6c757d" stroke-width="1.5">
                                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                                    <line x1="16" y1="2" x2="16" y2="6"/>
                                    <line x1="8" y1="2" x2="8" y2="6"/>
                                    <line x1="3" y1="10" x2="21" y2="10"/>
                                </svg>
                            </div>
                            <div>لا توجد مواعيد</div>
                            <div style="font-size: 10px; color: #999; margin-top: 8px;">ابدأ بإنشاء موعد جديد</div>
                        </div>
                    `;
                } else {
                    // ترتيب إضافي في JavaScript للتأكد من الترتيب الصحيح (updated for new schema)
                    const sortedAppointments = appointments.items.sort((a, b) => {
                        const dateA = new Date(a.startDate);
                        const dateB = new Date(b.startDate);
                        return dateA - dateB; // الأقرب أولاً
                    });

                    sortedAppointments.forEach(appointment => {
                        appointmentsList.innerHTML += createAppointmentCard(appointment, profiles);
                    });
                }
            } catch (error) {
                console.error('Failed to load appointments:', error);
                appointmentsList.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">❌</div>
                        <div>خطأ في تحميل المواعيد</div>
                        <div style="font-size: 10px; color: #999; margin-top: 8px;">${error.message}</div>
                    </div>
                `;
            }
        }

        // Get avatar URL for profile
        function getAvatarUrl(profile) {
            if (!profile || !profile.avatar) {
                return null;
            }

            try {
                return pb.files.getUrl(profile, profile.avatar);
            } catch (error) {
                console.warn('Could not get avatar URL:', error);
                return null;
            }
        }

        // Create appointment card HTML (updated for new schema with avatars)
        function createAppointmentCard(appointment, profiles = {}) {
            const owner = appointment.expand?.owner || { username: 'unknown' };
            const guests = appointment.expand?.guests || [];
            const daysRemaining = calculateDaysRemaining(appointment.startDate);
            const formattedDateTime = formatDateTime(appointment.startDate, appointment.time);
            const location = formatLocation(appointment.region, appointment.building);

            // Get owner profile and display info
            const ownerProfile = profiles[owner.id] || null;
            const ownerDisplayName = ownerProfile?.displayName || owner.username || 'مجهول';
            const ownerAvatarUrl = getAvatarUrl(ownerProfile);

            // Get first guest info if exists
            let guestAvatarUrl = null;
            let guestDisplayName = '';
            if (guests.length > 0) {
                const firstGuest = guests[0];
                const guestProfile = profiles[firstGuest.id] || null;
                guestDisplayName = guestProfile?.displayName || firstGuest.username || 'ضيف';
                guestAvatarUrl = getAvatarUrl(guestProfile);
            }

            // تحديد لون الأيام المتبقية
            let daysRemainingClass = '';
            if (daysRemaining === 'اليوم') {
                daysRemainingClass = 'today';
            } else if (daysRemaining === 'غداً') {
                daysRemainingClass = 'tomorrow';
            } else if (daysRemaining === 'بعد غد') {
                daysRemainingClass = 'day-after-tomorrow';
            }

            return `
                <div class="appointment-card" onclick="viewAppointment('${appointment.id}')">
                    <div class="card-header">
                        <div class="owner-info">
                            <div class="avatar">
                                ${ownerAvatarUrl ?
                                    `<img src="${ownerAvatarUrl}" alt="${ownerDisplayName}" class="avatar-img">` :
                                    '<div class="avatar-placeholder">👤</div>'
                                }
                            </div>
                            <div class="owner-name">${ownerDisplayName}</div>
                        </div>
                        <div class="days-remaining ${daysRemainingClass}">${daysRemaining}</div>
                    </div>
                    <div class="card-bottom">
                        <button class="copy-btn" onclick="event.stopPropagation(); copyAppointment('${appointment.id}')">نسخة</button>
                        <div class="appointment-details">
                            <div class="appointment-title">${appointment.title}</div>
                            <div class="location">${location}</div>
                            <div class="datetime">${formattedDateTime}</div>
                        </div>
                        ${guests.length > 0 ? `
                            <div class="guest-avatar" title="${guestDisplayName}">
                                ${guestAvatarUrl ?
                                    `<img src="${guestAvatarUrl}" alt="${guestDisplayName}" class="avatar-img">` :
                                    '<div class="avatar-placeholder">👤</div>'
                                }
                                <div class="status-indicator"></div>
                            </div>
                        ` : '<div style="width: 24px;"></div>'}
                    </div>
                </div>
            `;
        }

        // Calculate days remaining with better formatting (updated for new schema)
        function calculateDaysRemaining(startDate) {
            const appointmentDate = new Date(startDate);
            const today = new Date();
            today.setHours(0, 0, 0, 0); // بداية اليوم
            appointmentDate.setHours(0, 0, 0, 0); // بداية يوم الموعد

            const diffTime = appointmentDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays === 0) {
                return 'اليوم';
            } else if (diffDays === 1) {
                return 'غداً';
            } else if (diffDays === 2) {
                return 'بعد غد';
            } else if (diffDays > 0) {
                return `${diffDays} أيام`;
            } else {
                return 'منتهي';
            }
        }

        // Format date and time
        function formatDateTime(datetime, time) {
            const date = new Date(datetime);
            const weekdays = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
            const months = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            const weekday = weekdays[date.getDay()];
            const month = months[date.getMonth()];
            const formattedTime = formatTime12Hour(time);

            return `${weekday} ${date.getDate()} ${month} ${date.getFullYear()} - ${formattedTime}`;
        }

        // Format time to 12-hour format
        function formatTime12Hour(time24) {
            try {
                const [hour, minute] = time24.split(':');
                const h = parseInt(hour);

                if (h === 0) {
                    return `12:${minute} صباحاً`;
                } else if (h < 12) {
                    return `${h}:${minute} صباحاً`;
                } else if (h === 12) {
                    return `12:${minute} مساءً`;
                } else {
                    return `${h - 12}:${minute} مساءً`;
                }
            } catch (e) {
                return time24;
            }
        }

        // Format location
        function formatLocation(region, building) {
            if (building) {
                return `${region} - ${building}`;
            }
            return region;
        }

        // Navigation functions
        function showHome() {
            setActiveTab('home');
            document.getElementById('appBarTitle').textContent = 'مواعيدي';
            loadAppointments();
        }

        function showSearch() {
            setActiveTab('search');
            document.getElementById('appBarTitle').textContent = 'البحث';
            document.getElementById('appointmentsList').innerHTML = `
                <div style="padding: 20px;">
                    <input type="text" class="form-input" placeholder="ابحث عن المستخدمين..." style="margin-bottom: 16px;" onkeyup="searchUsers(this.value)">
                    <div id="searchResults" class="empty-state">
                        <div class="empty-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#6c757d" stroke-width="1.5">
                                <circle cx="11" cy="11" r="8"/>
                                <path d="m21 21-4.35-4.35"/>
                            </svg>
                        </div>
                        <div>ابحث عن المستخدمين</div>
                    </div>
                </div>
            `;
        }

        function showNotifications() {
            setActiveTab('notifications');
            document.getElementById('appBarTitle').textContent = 'الإشعارات';
            document.getElementById('appointmentsList').innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#6c757d" stroke-width="1.5">
                            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"/>
                            <path d="m13.73 21a2 2 0 0 1-3.46 0"/>
                        </svg>
                    </div>
                    <div>لا توجد إشعارات جديدة</div>
                </div>
            `;
        }

        function showSettings() {
            setActiveTab('settings');
            document.getElementById('appBarTitle').textContent = 'الإعدادات';
            document.getElementById('appointmentsList').innerHTML = `
                <div style="padding: 20px;">
                    <div style="padding: 16px; border-bottom: 1px solid #eee; cursor: pointer; display: flex; align-items: center; gap: 12px;" onclick="showProfile()">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#6c757d" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                        الملف الشخصي
                    </div>
                    <div style="padding: 16px; border-bottom: 1px solid #eee; cursor: pointer; display: flex; align-items: center; gap: 12px;" onclick="logout()">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#dc3545" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                            <polyline points="16,17 21,12 16,7"/>
                            <line x1="21" y1="12" x2="9" y2="12"/>
                        </svg>
                        تسجيل الخروج
                    </div>
                </div>
            `;
        }

        function setActiveTab(tab) {
            currentTab = tab;
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            document.querySelectorAll('.nav-item')[getTabIndex(tab)].classList.add('active');
        }

        function getTabIndex(tab) {
            const tabs = ['home', 'search', 'add', 'notifications', 'settings'];
            return tabs.indexOf(tab);
        }

        // Search users
        async function searchUsers(query) {
            const resultsEl = document.getElementById('searchResults');
            if (!query.trim()) {
                resultsEl.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#6c757d" stroke-width="1.5">
                                <circle cx="11" cy="11" r="8"/>
                                <path d="m21 21-4.35-4.35"/>
                            </svg>
                        </div>
                        <div>ابحث عن المستخدمين</div>
                    </div>
                `;
                return;
            }

            try {
                // Search in users only (no display_name in new schema)
                const users = await pb.collection('users').getList(1, 10, {
                    filter: `username ~ "${query}"`,
                });

                // Get profiles for found users
                const profiles = {};
                if (users.items.length > 0) {
                    try {
                        const userIds = users.items.map(user => user.id);
                        const profilesResult = await pb.collection('profiles').getList(1, 50, {
                            filter: userIds.map(id => `user.id = "${id}"`).join(' || '),
                            expand: 'user'
                        });

                        profilesResult.items.forEach(profile => {
                            profiles[profile.expand.user.id] = profile;
                        });
                    } catch (profileError) {
                        console.warn('Could not load profiles for search:', profileError);
                    }
                }

                if (users.items.length === 0) {
                    resultsEl.innerHTML = `
                        <div class="empty-state">
                            <div>لا توجد نتائج للبحث "${query}"</div>
                        </div>
                    `;
                } else {
                    resultsEl.innerHTML = users.items.map(user => {
                        const profile = profiles[user.id];
                        return `
                            <div style="padding: 12px; border-bottom: 1px solid #eee; display: flex; align-items: center; gap: 12px;">
                                <div class="avatar">👤</div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 500; font-size: 12px;">@${user.username}</div>
                                    ${profile?.bio ? `<div style="font-size: 10px; color: #6c757d;">${profile.bio}</div>` : ''}
                                    <div style="font-size: 9px; color: #adb5bd;">sijilli.com/${user.username}</div>
                                </div>
                            </div>
                        `;
                    }).join('');
                }
            } catch (error) {
                resultsEl.innerHTML = `
                    <div class="empty-state">
                        <div>خطأ في البحث: ${error.message}</div>
                    </div>
                `;
            }
        }

        // Other functions
        let selectedGuests = [];

        function addAppointment() {
            // Hide other content and show add screen
            document.getElementById('connectionStatus').style.display = 'none';
            document.getElementById('appointmentsList').style.display = 'none';
            document.getElementById('addScreen').style.display = 'block';

            // Set minimum date to today
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('appointmentDate').min = today;

            // Set default time to current time + 1 hour
            const now = new Date();
            now.setHours(now.getHours() + 1);
            const timeString = now.toTimeString().slice(0, 5);
            document.getElementById('appointmentTime').value = timeString;

            // Reset form
            document.getElementById('addAppointmentForm').reset();
            document.querySelector('input[name="privacy"][value="open"]').checked = true;
            updatePrivacySelection();

            // Clear guests
            selectedGuests = [];
            updateSelectedGuests();
        }

        // Close add appointment screen
        function closeAdd() {
            document.getElementById('addScreen').style.display = 'none';
            document.getElementById('connectionStatus').style.display = 'block';
            document.getElementById('appointmentsList').style.display = 'block';
        }

        // Update privacy selection UI
        function updatePrivacySelection() {
            const privacyOptions = document.querySelectorAll('.privacy-option');
            privacyOptions.forEach(option => {
                const radio = option.querySelector('input[type="radio"]');
                if (radio.checked) {
                    option.classList.add('selected');
                } else {
                    option.classList.remove('selected');
                }
            });
        }

        // Search for guests
        async function searchGuests(query) {
            const resultsEl = document.getElementById('guestSearchResults');

            if (!query.trim()) {
                resultsEl.style.display = 'none';
                return;
            }

            try {
                const users = await pb.collection('users').getList(1, 10, {
                    filter: `username ~ "${query}" && id != "${currentUser.id}"`,
                });

                if (users.items.length === 0) {
                    resultsEl.innerHTML = '<div class="guest-result">لا توجد نتائج</div>';
                } else {
                    resultsEl.innerHTML = users.items.map(user => `
                        <div class="guest-result" onclick="addGuest('${user.id}', '${user.username}')">
                            <div class="avatar">👤</div>
                            <div>
                                <div style="font-weight: 500; font-size: 12px;">@${user.username}</div>
                            </div>
                        </div>
                    `).join('');
                }
                resultsEl.style.display = 'block';
            } catch (error) {
                console.error('Error searching guests:', error);
                resultsEl.innerHTML = '<div class="guest-result">خطأ في البحث</div>';
                resultsEl.style.display = 'block';
            }
        }

        // Add guest to selection
        function addGuest(userId, username) {
            if (!selectedGuests.find(g => g.id === userId)) {
                selectedGuests.push({ id: userId, username });
                updateSelectedGuests();
            }

            // Clear search
            document.getElementById('guestSearch').value = '';
            document.getElementById('guestSearchResults').style.display = 'none';
        }

        // Remove guest from selection
        function removeGuest(userId) {
            selectedGuests = selectedGuests.filter(g => g.id !== userId);
            updateSelectedGuests();
        }

        // Update selected guests UI
        function updateSelectedGuests() {
            const container = document.getElementById('selectedGuests');
            container.innerHTML = selectedGuests.map(guest => `
                <div class="guest-tag">
                    @${guest.username}
                    <span class="remove-guest" onclick="removeGuest('${guest.id}')">×</span>
                </div>
            `).join('');
        }

        // Save appointment
        async function saveAppointment(event) {
            event.preventDefault();

            const saveBtn = document.getElementById('saveAppointmentBtn');
            const originalText = saveBtn.textContent;

            try {
                saveBtn.disabled = true;
                saveBtn.textContent = 'جاري الحفظ...';

                // Check if user is logged in
                if (!currentUser) {
                    throw new Error('يجب تسجيل الدخول أولاً');
                }

                // Get form data
                const title = document.getElementById('appointmentTitle').value.trim();
                const privacy = document.querySelector('input[name="privacy"]:checked').value;
                const region = document.getElementById('appointmentRegion').value.trim();
                const building = document.getElementById('appointmentBuilding').value.trim();
                const date = document.getElementById('appointmentDate').value;
                const time = document.getElementById('appointmentTime').value;

                // Validation
                if (!title || !region || !date || !time) {
                    throw new Error('يرجى ملء جميع الحقول المطلوبة');
                }

                // Prepare appointment data (new schema)
                const appointmentData = {
                    title,
                    privacy,
                    region,
                    building: building || '',
                    calendarType: 'gregorian', // افتراضي ميلادي
                    startDate: date,
                    endDate: date, // نفس التاريخ للمواعيد اليومية
                    hasTime: true,
                    time: time,
                    owner: currentUser.id,
                    guests: selectedGuests.map(g => g.id),
                    status: 'active'
                };

                // Create appointment
                console.log('Creating appointment with data:', appointmentData);
                const appointment = await pb.collection('appointments').create(appointmentData);
                console.log('Appointment created successfully:', appointment);

                // Create guest status records and send notifications
                for (const guest of selectedGuests) {
                    try {
                        // Create guest status record
                        await pb.collection('appointment_guests_status').create({
                            appointment: appointment.id,
                            user: guest.id,
                            status: 'invited',
                            note: ''
                        });

                        // Send notification
                        await pb.collection('inbox').create({
                            title: `دعوة لموعد: ${title}`,
                            message: `تم دعوتك لموعد "${title}" في ${region}`,
                            type: 'invitation',
                            owner: guest.id, // المستلم
                            appointment: appointment.id,
                            relatedUser: currentUser.id, // المرسل
                            read: false
                        });
                    } catch (notificationError) {
                        console.warn('Failed to send notification to guest:', guest.username, notificationError);
                    }
                }

                showNotification('تم إنشاء الموعد بنجاح!', 'success');

                // Close add screen and return to home
                document.getElementById('addScreen').style.display = 'none';
                document.getElementById('connectionStatus').style.display = 'block';
                document.getElementById('appointmentsList').style.display = 'block';

                // Refresh appointments list
                await loadAppointments();

            } catch (error) {
                console.error('Error creating appointment:', error);
                showNotification(`خطأ في إنشاء الموعد: ${error.message}`, 'error');
            } finally {
                saveBtn.disabled = false;
                saveBtn.textContent = originalText;
            }
        }

        function viewAppointment(id) {
            showNotification(`عرض تفاصيل الموعد رقم ${id}`, 'info');
        }

        function copyAppointment(id) {
            showNotification('تم نسخ بيانات الموعد', 'success');
        }

        function refreshAppointments() {
            if (currentTab === 'home') {
                loadAppointments();
                showNotification('تم تحديث المواعيد', 'success');
            }
        }

        function logout() {
            pb.authStore.clear();
            currentUser = null;
            document.getElementById('loginScreen').style.display = 'flex';
            document.getElementById('mainApp').style.display = 'none';
            document.getElementById('emailInput').value = '';
            document.getElementById('passwordInput').value = '';
            showNotification('تم تسجيل الخروج', 'info');
        }

        // Register functions
        function showRegister() {
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('registerScreen').style.display = 'flex';

            // Reset form
            resetRegisterForm();
        }

        function showLogin() {
            document.getElementById('registerScreen').style.display = 'none';
            document.getElementById('loginScreen').style.display = 'flex';
        }

        function resetRegisterForm() {
            // Clear all inputs
            document.getElementById('registerUsername').value = '';
            document.getElementById('registerEmail').value = '';
            document.getElementById('registerPassword').value = '';
            document.getElementById('registerPasswordConfirm').value = '';
            document.getElementById('registerDisplayName').value = '';
            document.getElementById('registerJob').value = '';
            document.getElementById('registerPhone').value = '';
            document.getElementById('registerBio').value = '';

            // Reset to step 1
            document.getElementById('registerStep1').style.display = 'block';
            document.getElementById('registerStep2').style.display = 'none';

            // Clear error messages
            document.getElementById('registerErrorMessage').style.display = 'none';
        }

        function nextStep() {
            const username = document.getElementById('registerUsername').value.trim();
            const email = document.getElementById('registerEmail').value.trim();
            const password = document.getElementById('registerPassword').value;
            const passwordConfirm = document.getElementById('registerPasswordConfirm').value;

            try {
                // Validate step 1
                if (!username) {
                    throw new Error('اسم المستخدم مطلوب');
                }

                if (!isValidUsername(username)) {
                    throw new Error('اسم المستخدم غير صحيح. يجب أن يحتوي على حروف إنجليزية وأرقام ونقطة وأندرسكور فقط');
                }

                if (!email) {
                    throw new Error('البريد الإلكتروني مطلوب');
                }

                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    throw new Error('البريد الإلكتروني غير صحيح');
                }

                if (!password) {
                    throw new Error('كلمة المرور مطلوبة');
                }

                if (password.length < 8) {
                    throw new Error('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                }

                if (password !== passwordConfirm) {
                    throw new Error('كلمة المرور وتأكيدها غير متطابقتين');
                }

                // Move to step 2
                document.getElementById('registerStep1').style.display = 'none';
                document.getElementById('registerStep2').style.display = 'block';
                document.getElementById('registerErrorMessage').style.display = 'none';

            } catch (error) {
                showRegisterError(error.message);
            }
        }

        function prevStep() {
            document.getElementById('registerStep2').style.display = 'none';
            document.getElementById('registerStep1').style.display = 'block';
            document.getElementById('registerErrorMessage').style.display = 'none';
        }

        async function register() {
            const registerBtn = document.getElementById('registerBtn');
            const originalText = registerBtn.textContent;

            try {
                registerBtn.disabled = true;
                registerBtn.textContent = 'جاري إنشاء الحساب...';

                // Get form data
                const username = document.getElementById('registerUsername').value.trim();
                const email = document.getElementById('registerEmail').value.trim();
                const password = document.getElementById('registerPassword').value;
                const displayName = document.getElementById('registerDisplayName').value.trim();
                const job = document.getElementById('registerJob').value.trim();
                const phone = document.getElementById('registerPhone').value.trim();
                const bio = document.getElementById('registerBio').value.trim();

                console.log('Attempting to register user:', { username, email });

                // Check if username is available
                try {
                    const existingUsers = await pb.collection('users').getList(1, 1, {
                        filter: `username = "${username}"`,
                    });

                    if (existingUsers.items.length > 0) {
                        throw new Error('اسم المستخدم مستخدم بالفعل');
                    }
                } catch (checkError) {
                    if (checkError.message === 'اسم المستخدم مستخدم بالفعل') {
                        throw checkError;
                    }
                    console.warn('Could not check username availability:', checkError);
                }

                // Check if email is available
                try {
                    const existingEmails = await pb.collection('users').getList(1, 1, {
                        filter: `email = "${email}"`,
                    });

                    if (existingEmails.items.length > 0) {
                        throw new Error('البريد الإلكتروني مستخدم بالفعل');
                    }
                } catch (checkError) {
                    if (checkError.message === 'البريد الإلكتروني مستخدم بالفعل') {
                        throw checkError;
                    }
                    console.warn('Could not check email availability:', checkError);
                }

                // Create user account
                const userData = {
                    username: username,
                    email: email,
                    password: password,
                    passwordConfirm: password,
                    role: 'user',
                    isPublic: true,
                    verified: false
                };

                console.log('Creating user with data:', userData);
                const user = await pb.collection('users').create(userData);
                console.log('User created successfully:', user);

                // Create profile if any profile data provided
                if (displayName || job || phone || bio) {
                    try {
                        const profileData = {
                            user: user.id,
                            displayName: displayName || '',
                            job: job || '',
                            phone: phone || '',
                            bio: bio || '',
                            instagram: '',
                            youtube: ''
                        };

                        console.log('Creating profile with data:', profileData);
                        const profile = await pb.collection('profiles').create(profileData);
                        console.log('Profile created successfully:', profile);
                    } catch (profileError) {
                        console.warn('Could not create profile, but user was created:', profileError);
                        // Don't fail registration if profile creation fails
                    }
                }

                // Auto-login the new user
                try {
                    await pb.collection('users').authWithPassword(email, password);
                    currentUser = pb.authStore.model;
                    console.log('Auto-login successful:', currentUser);

                    showNotification('تم إنشاء الحساب بنجاح! مرحباً بك في سجلي', 'success');

                    // Hide register screen and show main app
                    document.getElementById('registerScreen').style.display = 'none';
                    document.getElementById('mainApp').style.display = 'block';

                    // Load initial data
                    await loadAppointments();

                } catch (loginError) {
                    console.warn('Auto-login failed, but account was created:', loginError);
                    showNotification('تم إنشاء الحساب بنجاح! يرجى تسجيل الدخول', 'success');
                    showLogin();
                }

            } catch (error) {
                console.error('Registration failed:', error);
                showRegisterError('خطأ في إنشاء الحساب: ' + error.message);
            } finally {
                registerBtn.disabled = false;
                registerBtn.textContent = originalText;
            }
        }

        function showRegisterError(message) {
            const errorEl = document.getElementById('registerErrorMessage');
            errorEl.textContent = message;
            errorEl.style.display = 'block';

            // Scroll to top to show error
            document.getElementById('registerScreen').scrollTop = 0;
        }

        // Profile functions
        function showProfile() {
            if (!currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            console.log('Showing profile for user:', currentUser);

            // Hide main app and show profile
            document.getElementById('mainApp').style.display = 'none';
            document.getElementById('profileScreen').style.display = 'block';

            loadProfileData();
        }

        function closeProfile() {
            document.getElementById('profileScreen').style.display = 'none';
            document.getElementById('mainApp').style.display = 'block';
            cancelEdit(); // Reset any editing state
        }

        async function loadProfileData() {
            try {
                console.log('Loading profile data for user:', currentUser.id);

                // Get fresh user data
                const userData = await pb.collection('users').getOne(currentUser.id);
                currentUser = userData;

                console.log('User data loaded:', userData);

                // Get or create profile data (updated schema)
                let profileData = null;
                try {
                    const profiles = await pb.collection('profiles').getList(1, 1, {
                        filter: `user.id = "${currentUser.id}"`,
                        expand: 'user'
                    });

                    if (profiles.items.length > 0) {
                        profileData = profiles.items[0];
                    } else {
                        // Create new profile if doesn't exist (new schema)
                        profileData = await pb.collection('profiles').create({
                            user: currentUser.id,
                            displayName: '',
                            job: '',
                            phone: '',
                            bio: '',
                            instagram: '',
                            youtube: ''
                        });
                    }
                } catch (profileError) {
                    console.error('Profile error:', profileError);
                    // Fallback to user data only
                    profileData = { bio: '', instagram: '', youtube: '' };
                }

                // Update profile display (with display name)
                const displayName = profileData.displayName || userData.username;
                document.getElementById('profileDisplayName').textContent = displayName;
                document.getElementById('profileUsername').textContent = `@${userData.username}`;
                document.getElementById('profileEmail').textContent = userData.email;

                // Update avatar
                updateAvatarDisplay(profileData.avatar);

                // Update profile fields
                document.getElementById('usernameView').textContent = userData.username;
                document.getElementById('emailView').textContent = userData.email;

                // Update display name field
                const displayNameEl = document.getElementById('displayNameView');
                if (profileData.displayName) {
                    displayNameEl.textContent = profileData.displayName;
                    displayNameEl.classList.remove('empty');
                } else {
                    displayNameEl.textContent = 'لم يتم تعيين اسم عرض';
                    displayNameEl.classList.add('empty');
                }

                // Update job field
                const jobEl = document.getElementById('jobView');
                if (profileData.job) {
                    jobEl.textContent = profileData.job;
                    jobEl.classList.remove('empty');
                } else {
                    jobEl.textContent = 'لم يتم تحديد المهنة';
                    jobEl.classList.add('empty');
                }

                // Update phone field
                const phoneEl = document.getElementById('phoneView');
                if (profileData.phone) {
                    phoneEl.textContent = profileData.phone;
                    phoneEl.classList.remove('empty');
                } else {
                    phoneEl.textContent = 'لم يتم إضافة رقم هاتف';
                    phoneEl.classList.add('empty');
                }

                // Update bio field
                const bioEl = document.getElementById('bioView');
                if (profileData.bio) {
                    bioEl.textContent = profileData.bio;
                    bioEl.classList.remove('empty');
                } else {
                    bioEl.textContent = 'لم يتم إضافة نبذة شخصية';
                    bioEl.classList.add('empty');
                }

                // Update social links
                document.getElementById('instagramView').textContent = profileData.instagram || 'لم يتم إضافة رابط';
                document.getElementById('youtubeView').textContent = profileData.youtube || 'لم يتم إضافة رابط';

                // Store profile data for later use
                currentUser.profile = profileData;

                // Update dates
                const createdDate = new Date(userData.created).toLocaleDateString('ar-SA');
                const updatedDate = new Date(userData.updated).toLocaleDateString('ar-SA');
                document.getElementById('createdDate').textContent = createdDate;
                document.getElementById('updatedDate').textContent = updatedDate;
                document.getElementById('joinedDate').textContent = new Date(userData.created).getFullYear();

                // Update verification status
                const verifiedEl = document.getElementById('verifiedStatus');
                if (userData.verified) {
                    verifiedEl.innerHTML = '<span style="color: #28a745;">✓ محقق</span>';
                } else {
                    verifiedEl.innerHTML = '<span style="color: #dc3545;">✗ غير محقق</span>';
                }

                // Load statistics
                await loadProfileStats();

            } catch (error) {
                console.error('Failed to load profile:', error);
                showNotification('خطأ في تحميل البيانات', 'error');
            }
        }

        async function loadProfileStats() {
            try {
                // Get appointments count
                const appointmentsResult = await pb.collection('appointments').getList(1, 1, {
                    filter: `owner.id = "${currentUser.id}"`,
                });
                document.getElementById('appointmentsCount').textContent = appointmentsResult.totalItems;

                // Get invitations count (as guest)
                const invitationsResult = await pb.collection('appointments').getList(1, 1, {
                    filter: `guests.id ?= "${currentUser.id}"`,
                });
                document.getElementById('invitationsCount').textContent = invitationsResult.totalItems;

            } catch (error) {
                console.error('Failed to load stats:', error);
                document.getElementById('appointmentsCount').textContent = '0';
                document.getElementById('invitationsCount').textContent = '0';
            }
        }

        function toggleEdit() {
            const isEditing = document.getElementById('displayNameEdit').style.display !== 'none';

            if (isEditing) {
                cancelEdit();
            } else {
                startEdit();
            }
        }

        function startEdit() {
            console.log('Starting edit mode');
            console.log('Current user profile:', currentUser.profile);

            // Hide view elements and show edit inputs
            document.getElementById('displayNameView').style.display = 'none';
            document.getElementById('usernameView').style.display = 'none';
            document.getElementById('emailView').style.display = 'none';
            document.getElementById('jobView').style.display = 'none';
            document.getElementById('phoneView').style.display = 'none';
            document.getElementById('bioView').style.display = 'none';
            document.getElementById('instagramView').style.display = 'none';
            document.getElementById('youtubeView').style.display = 'none';

            document.getElementById('displayNameEdit').style.display = 'block';
            document.getElementById('usernameEdit').style.display = 'block';
            document.getElementById('emailEdit').style.display = 'block';
            document.getElementById('jobEdit').style.display = 'block';
            document.getElementById('phoneEdit').style.display = 'block';
            document.getElementById('bioEdit').style.display = 'block';
            document.getElementById('instagramEdit').style.display = 'block';
            document.getElementById('youtubeEdit').style.display = 'block';

            // Set current values - كل حقل منفصل ومستقل
            document.getElementById('displayNameEdit').value = currentUser.profile?.displayName || '';
            document.getElementById('usernameEdit').value = currentUser.username;
            document.getElementById('emailEdit').value = currentUser.email;
            document.getElementById('jobEdit').value = currentUser.profile?.job || '';
            document.getElementById('phoneEdit').value = currentUser.profile?.phone || '';
            document.getElementById('bioEdit').value = currentUser.profile?.bio || '';
            document.getElementById('instagramEdit').value = currentUser.profile?.instagram || '';
            document.getElementById('youtubeEdit').value = currentUser.profile?.youtube || '';

            // Add real-time validation for username
            const usernameInput = document.getElementById('usernameEdit');
            usernameInput.addEventListener('input', validateUsernameInput);

            // Show username hints
            document.getElementById('usernameHint').style.display = 'block';
            document.getElementById('usernameExample').style.display = 'block';

            // Update buttons
            document.getElementById('editBtn').style.display = 'none';
            document.getElementById('cancelBtn').style.display = 'block';
            document.getElementById('saveBtn').style.display = 'block';
        }

        // Validate username input in real-time
        function validateUsernameInput(event) {
            const input = event.target;
            const value = input.value;

            // Remove invalid characters
            const validValue = value.replace(/[^a-zA-Z0-9._]/g, '');

            if (value !== validValue) {
                input.value = validValue;
                showNotification('اسم المستخدم يجب أن يحتوي على حروف إنجليزية وأرقام ونقطة وأندرسكور فقط', 'error');
            }

            // Update border color based on validity
            if (isValidUsername(validValue)) {
                input.style.borderColor = '#28a745';
            } else {
                input.style.borderColor = '#dc3545';
            }
        }

        // Check if username is valid
        function isValidUsername(username) {
            // Must contain only English letters, numbers, dots, and underscores
            const usernameRegex = /^[a-zA-Z0-9._]+$/;

            // Must be at least 3 characters long
            if (username.length < 3) return false;

            // Must not start or end with dot or underscore
            if (username.startsWith('.') || username.startsWith('_') ||
                username.endsWith('.') || username.endsWith('_')) return false;

            // Must not have consecutive dots or underscores
            if (username.includes('..') || username.includes('__') ||
                username.includes('._') || username.includes('_.')) return false;

            return usernameRegex.test(username);
        }

        function cancelEdit() {
            // Show view elements and hide edit inputs
            document.getElementById('displayNameView').style.display = 'flex';
            document.getElementById('usernameView').style.display = 'flex';
            document.getElementById('emailView').style.display = 'flex';
            document.getElementById('jobView').style.display = 'flex';
            document.getElementById('phoneView').style.display = 'flex';
            document.getElementById('bioView').style.display = 'flex';
            document.getElementById('instagramView').style.display = 'flex';
            document.getElementById('youtubeView').style.display = 'flex';

            document.getElementById('displayNameEdit').style.display = 'none';
            document.getElementById('usernameEdit').style.display = 'none';
            document.getElementById('emailEdit').style.display = 'none';
            document.getElementById('jobEdit').style.display = 'none';
            document.getElementById('phoneEdit').style.display = 'none';
            document.getElementById('bioEdit').style.display = 'none';
            document.getElementById('instagramEdit').style.display = 'none';
            document.getElementById('youtubeEdit').style.display = 'none';

            // Hide username hints
            document.getElementById('usernameHint').style.display = 'none';
            document.getElementById('usernameExample').style.display = 'none';

            // Reset input styles
            document.getElementById('usernameEdit').style.borderColor = '#dee2e6';

            // Update buttons
            document.getElementById('editBtn').style.display = 'block';
            document.getElementById('cancelBtn').style.display = 'none';
            document.getElementById('saveBtn').style.display = 'none';
        }

        async function saveProfile() {
            const saveBtn = document.getElementById('saveBtn');
            const originalText = saveBtn.textContent;

            try {
                saveBtn.disabled = true;
                saveBtn.textContent = 'جاري الحفظ...';

                // Check if user is logged in
                if (!currentUser) {
                    throw new Error('يجب تسجيل الدخول أولاً');
                }

                console.log('Current user:', currentUser);

                const newDisplayName = document.getElementById('displayNameEdit').value.trim();
                const newUsername = document.getElementById('usernameEdit').value.trim();
                const newEmail = document.getElementById('emailEdit').value.trim();
                const newJob = document.getElementById('jobEdit').value.trim();
                const newPhone = document.getElementById('phoneEdit').value.trim();
                const newBio = document.getElementById('bioEdit').value.trim();
                const newInstagram = document.getElementById('instagramEdit').value.trim();
                const newYoutube = document.getElementById('youtubeEdit').value.trim();

                // Validate inputs - الاسم المعروض اختياري
                // يمكن ترك الاسم المعروض فارغاً

                if (!newUsername) {
                    throw new Error('اسم المستخدم مطلوب');
                }

                if (!newEmail) {
                    throw new Error('البريد الإلكتروني مطلوب');
                }

                // Validate username format
                if (!isValidUsername(newUsername)) {
                    throw new Error('اسم المستخدم غير صحيح. يجب أن يحتوي على حروف إنجليزية وأرقام ونقطة وأندرسكور فقط، ولا يبدأ أو ينتهي بنقطة أو أندرسكور');
                }

                // Validate email format
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(newEmail)) {
                    throw new Error('البريد الإلكتروني غير صحيح');
                }

                // Check if username is already taken (if changed)
                if (newUsername !== currentUser.username) {
                    try {
                        const existingUsers = await pb.collection('users').getList(1, 1, {
                            filter: `username = "${newUsername}"`,
                        });

                        if (existingUsers.items.length > 0) {
                            throw new Error('اسم المستخدم مستخدم بالفعل');
                        }
                    } catch (checkError) {
                        if (checkError.message !== 'اسم المستخدم مستخدم بالفعل') {
                            console.warn('Could not check username availability:', checkError);
                        } else {
                            throw checkError;
                        }
                    }
                }

                // Prepare update data - فصل بيانات المستخدم عن البروفايل
                const userUpdateData = {};
                const profileUpdateData = {};

                // تحديث البريد الإلكتروني في جدول المستخدمين
                if (newEmail !== currentUser.email) {
                    userUpdateData.email = newEmail;
                }

                // تحديث اسم المستخدم في جدول المستخدمين
                if (newUsername !== currentUser.username) {
                    userUpdateData.username = newUsername;
                }

                // تحديث بيانات البروفايل
                if (newDisplayName !== (currentUser.profile?.displayName || '')) {
                    profileUpdateData.displayName = newDisplayName;
                }

                if (newJob !== (currentUser.profile?.job || '')) {
                    profileUpdateData.job = newJob;
                }

                if (newPhone !== (currentUser.profile?.phone || '')) {
                    profileUpdateData.phone = newPhone;
                }

                if (newBio !== (currentUser.profile?.bio || '')) {
                    profileUpdateData.bio = newBio;
                }

                if (newInstagram !== (currentUser.profile?.instagram || '')) {
                    profileUpdateData.instagram = newInstagram;
                }

                if (newYoutube !== (currentUser.profile?.youtube || '')) {
                    profileUpdateData.youtube = newYoutube;
                }

                // التأكد من وجود تغييرات للحفظ
                if (Object.keys(userUpdateData).length === 0 && Object.keys(profileUpdateData).length === 0) {
                    throw new Error('لا توجد تغييرات للحفظ');
                }

                // Update user data if needed
                let updatedUser = currentUser;
                if (Object.keys(userUpdateData).length > 0) {
                    updatedUser = await pb.collection('users').update(currentUser.id, userUpdateData);
                }

                // Update profile data if needed
                let updatedProfile = currentUser.profile;
                if (Object.keys(profileUpdateData).length > 0) {
                    if (currentUser.profile && currentUser.profile.id) {
                        updatedProfile = await pb.collection('profiles').update(currentUser.profile.id, profileUpdateData);
                    } else {
                        // Create new profile if doesn't exist (new schema)
                        updatedProfile = await pb.collection('profiles').create({
                            user: currentUser.id,
                            displayName: newDisplayName,
                            job: newJob,
                            phone: newPhone,
                            bio: newBio,
                            instagram: newInstagram,
                            youtube: newYoutube
                        });
                    }
                }

                // Update current user data
                currentUser = updatedUser;
                currentUser.profile = updatedProfile;

                // Refresh profile display
                await loadProfileData();
                cancelEdit();

                showNotification('تم حفظ التغييرات بنجاح - سيتم الانتقال للصفحة الرئيسية', 'success');

                // الانتقال للصفحة الرئيسية بعد ثانيتين
                setTimeout(() => {
                    closeProfile();
                    showHome();
                }, 2000);

            } catch (error) {
                console.error('Failed to save profile:', error);
                showNotification('خطأ في حفظ التغييرات: ' + error.message, 'error');
            } finally {
                saveBtn.disabled = false;
                saveBtn.textContent = originalText;
            }
        }

        // Avatar functions
        function updateAvatarDisplay(avatarUrl) {
            const avatarImage = document.getElementById('avatarImage');
            const avatarPlaceholder = document.getElementById('avatarPlaceholder');
            const removeBtn = document.getElementById('removeAvatarBtn');
            const btnText = document.getElementById('avatarBtnText');

            if (avatarUrl) {
                avatarImage.src = pb.files.getUrl(currentUser.profile, avatarUrl);
                avatarImage.style.display = 'block';
                avatarPlaceholder.style.display = 'none';
                removeBtn.style.display = 'block';
                btnText.textContent = 'تغيير الصورة';
            } else {
                avatarImage.style.display = 'none';
                avatarPlaceholder.style.display = 'block';
                removeBtn.style.display = 'none';
                btnText.textContent = 'اختيار صورة';
            }
        }

        async function handleAvatarChange(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Validate file type
            if (!file.type.startsWith('image/')) {
                showNotification('يرجى اختيار ملف صورة صحيح', 'error');
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                showNotification('حجم الصورة كبير جداً. الحد الأقصى 5 ميجابايت', 'error');
                return;
            }

            try {
                showNotification('جاري رفع الصورة...', 'info');

                // Create FormData for file upload
                const formData = new FormData();
                formData.append('avatar', file);

                // Update profile with new avatar
                if (currentUser.profile && currentUser.profile.id) {
                    const updatedProfile = await pb.collection('profiles').update(currentUser.profile.id, formData);
                    currentUser.profile = updatedProfile;
                    updateAvatarDisplay(updatedProfile.avatar);
                    showNotification('تم تحديث الصورة الشخصية بنجاح', 'success');
                } else {
                    showNotification('يرجى حفظ البروفايل أولاً', 'error');
                }
            } catch (error) {
                console.error('Avatar upload error:', error);
                showNotification('خطأ في رفع الصورة: ' + error.message, 'error');
            }

            // Clear input
            event.target.value = '';
        }

        async function removeAvatar() {
            try {
                showNotification('جاري حذف الصورة...', 'info');

                if (currentUser.profile && currentUser.profile.id) {
                    const updatedProfile = await pb.collection('profiles').update(currentUser.profile.id, {
                        avatar: null
                    });
                    currentUser.profile = updatedProfile;
                    updateAvatarDisplay(null);
                    showNotification('تم حذف الصورة الشخصية', 'success');
                }
            } catch (error) {
                console.error('Avatar removal error:', error);
                showNotification('خطأ في حذف الصورة: ' + error.message, 'error');
            }
        }

        function changeAvatar() {
            document.getElementById('avatarInput').click();
        }

        function showNotification(message, type = 'info') {
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                info: '#495057'
            };

            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: ${colors[type]};
                color: white;
                padding: 12px 24px;
                border-radius: 4px;
                z-index: 1000;
                font-size: 12px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 3000);
        }

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initPocketbase();

            // Allow Enter key to login
            document.getElementById('passwordInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    login();
                }
            });

            // Setup add appointment form
            setupAddAppointmentForm();
        });

        // Setup add appointment form event listeners
        function setupAddAppointmentForm() {
            // Add appointment form
            const addForm = document.getElementById('addAppointmentForm');
            if (addForm) {
                addForm.addEventListener('submit', saveAppointment);
            }

            // Privacy options
            const privacyOptions = document.querySelectorAll('.privacy-option');
            privacyOptions.forEach(option => {
                option.addEventListener('click', () => {
                    const radio = option.querySelector('input[type="radio"]');
                    radio.checked = true;
                    updatePrivacySelection();
                });
            });

            // Guest search - hide results when clicking outside
            document.addEventListener('click', (e) => {
                const guestSearch = document.getElementById('guestSearch');
                const guestResults = document.getElementById('guestSearchResults');

                if (guestSearch && guestResults &&
                    !guestSearch.contains(e.target) &&
                    !guestResults.contains(e.target)) {
                    guestResults.style.display = 'none';
                }
            });
        }

        // Debug: Check if functions exist
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, checking functions...');
            console.log('login function exists:', typeof login);
            console.log('showRegister function exists:', typeof showRegister);
            console.log('pb variable:', typeof pb);
            console.log('currentUser variable:', typeof currentUser);

            // List all global functions
            const globalFunctions = [];
            for (let prop in window) {
                if (typeof window[prop] === 'function' && prop.includes('login') || prop.includes('register')) {
                    globalFunctions.push(prop);
                }
            }
            console.log('Functions with login/register:', globalFunctions);
        });
    </script>
</body>
</html>
