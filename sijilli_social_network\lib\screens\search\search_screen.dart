import 'package:flutter/material.dart';
import '../../constants/app_theme.dart';
import '../../services/pocketbase_service.dart';
import '../../models/models.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final _searchController = TextEditingController();
  List<User> _searchResults = [];
  bool _isLoading = false;
  bool _hasSearched = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('البحث'),
      ),
      body: Column(
        children: [
          // Search Field
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث عن المستخدمين...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchResults.clear();
                            _hasSearched = false;
                          });
                        },
                      )
                    : null,
              ),
              onChanged: (value) {
                setState(() {});
                if (value.isNotEmpty) {
                  _performSearch(value);
                } else {
                  setState(() {
                    _searchResults.clear();
                    _hasSearched = false;
                  });
                }
              },
              onSubmitted: _performSearch,
            ),
          ),
          
          // Search Results
          Expanded(
            child: _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (!_hasSearched) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.search,
              size: 64,
              color: AppTheme.textHint,
            ),
            const SizedBox(height: 16),
            Text(
              'ابحث عن المستخدمين',
              style: AppTheme.headlineSmall.copyWith(color: AppTheme.textSecondary),
            ),
            const SizedBox(height: 8),
            Text(
              'يمكنك البحث باستخدام اسم المستخدم أو الاسم الظاهر',
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.textHint),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.person_search,
              size: 64,
              color: AppTheme.textHint,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج',
              style: AppTheme.headlineSmall.copyWith(color: AppTheme.textSecondary),
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على مستخدمين بهذا الاسم',
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.textHint),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final user = _searchResults[index];
        return _buildUserCard(user);
      },
    );
  }

  Widget _buildUserCard(User user) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppTheme.primaryColor,
          child: Text(
            user.username[0].toUpperCase(),
            style: AppTheme.labelLarge.copyWith(color: Colors.white),
          ),
        ),
        title: Text(
          user.displayName ?? user.username,
          style: AppTheme.bodyLarge,
        ),
        subtitle: Text(
          '@${user.username}',
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
        ),
        trailing: _buildUserActions(user),
        onTap: () => _navigateToProfile(user),
      ),
    );
  }

  Widget _buildUserActions(User user) {
    final currentUser = PocketbaseService.instance.currentUser;
    
    // Don't show actions for current user
    if (currentUser?.id == user.id) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Follow button (placeholder)
        OutlinedButton(
          onPressed: () => _toggleFollow(user),
          style: OutlinedButton.styleFrom(
            minimumSize: const Size(80, 32),
            padding: const EdgeInsets.symmetric(horizontal: 12),
          ),
          child: const Text('متابعة'),
        ),
      ],
    );
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) return;

    setState(() {
      _isLoading = true;
      _hasSearched = true;
    });

    try {
      final results = await PocketbaseService.instance.searchUsers(query.trim());
      setState(() {
        _searchResults = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _searchResults = [];
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في البحث: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _toggleFollow(User user) {
    // TODO: Implement follow/unfollow functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم متابعة ${user.username}')),
    );
  }

  void _navigateToProfile(User user) {
    // TODO: Navigate to user profile
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض ملف ${user.username}')),
    );
  }
}
