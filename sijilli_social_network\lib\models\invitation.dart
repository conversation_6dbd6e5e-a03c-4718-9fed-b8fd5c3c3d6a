import 'user.dart';
import 'appointment.dart';

class Invitation {
  final String id;
  final String appointmentId;
  final User inviter;
  final User invitee;
  final InvitationStatus status;
  final DateTime created;
  final DateTime updated;
  final DateTime? respondedAt;

  // Expanded relations
  Appointment? appointment;

  Invitation({
    required this.id,
    required this.appointmentId,
    required this.inviter,
    required this.invitee,
    required this.status,
    required this.created,
    required this.updated,
    this.respondedAt,
    this.appointment,
  });

  factory Invitation.fromJson(Map<String, dynamic> json) {
    return Invitation(
      id: json['id'] ?? '',
      appointmentId: json['appointmentId'] ?? '',
      inviter: User.fromJson(json['expand']?['inviter'] ?? {}),
      invitee: User.fromJson(json['expand']?['invitee'] ?? {}),
      status: InvitationStatus.fromString(json['status'] ?? 'pending'),
      created: DateTime.parse(json['created']),
      updated: DateTime.parse(json['updated']),
      respondedAt: json['respondedAt'] != null 
          ? DateTime.parse(json['respondedAt']) 
          : null,
      appointment: json['expand']?['appointment'] != null
          ? Appointment.fromJson(json['expand']['appointment'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'appointmentId': appointmentId,
      'inviter': inviter.id,
      'invitee': invitee.id,
      'status': status.toString(),
      'created': created.toIso8601String(),
      'updated': updated.toIso8601String(),
      'respondedAt': respondedAt?.toIso8601String(),
    };
  }

  Invitation copyWith({
    String? id,
    String? appointmentId,
    User? inviter,
    User? invitee,
    InvitationStatus? status,
    DateTime? created,
    DateTime? updated,
    DateTime? respondedAt,
    Appointment? appointment,
  }) {
    return Invitation(
      id: id ?? this.id,
      appointmentId: appointmentId ?? this.appointmentId,
      inviter: inviter ?? this.inviter,
      invitee: invitee ?? this.invitee,
      status: status ?? this.status,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      respondedAt: respondedAt ?? this.respondedAt,
      appointment: appointment ?? this.appointment,
    );
  }

  // Helper methods
  bool get isPending => status == InvitationStatus.pending;
  bool get isAccepted => status == InvitationStatus.accepted;
  bool get isRejected => status == InvitationStatus.rejected;
  bool get isDeleted => status == InvitationStatus.deleted;

  @override
  String toString() {
    return 'Invitation(id: $id, status: $status, inviter: ${inviter.username}, invitee: ${invitee.username})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Invitation && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum InvitationStatus {
  pending,    // مدعو - لم يقرر بعد (رمادي)
  accepted,   // قبل الموعد (أخضر)
  rejected,   // رفض الموعد
  deleted;    // قبل ثم حذف الموعد (أحمر)

  static InvitationStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return InvitationStatus.pending;
      case 'accepted':
        return InvitationStatus.accepted;
      case 'rejected':
        return InvitationStatus.rejected;
      case 'deleted':
        return InvitationStatus.deleted;
      default:
        return InvitationStatus.pending;
    }
  }

  @override
  String toString() {
    return name;
  }

  String get displayName {
    switch (this) {
      case InvitationStatus.pending:
        return 'مدعو';
      case InvitationStatus.accepted:
        return 'قبل';
      case InvitationStatus.rejected:
        return 'رفض';
      case InvitationStatus.deleted:
        return 'قبل ثم حذف';
    }
  }

  String get colorCode {
    switch (this) {
      case InvitationStatus.pending:
        return 'gray';
      case InvitationStatus.accepted:
        return 'green';
      case InvitationStatus.rejected:
        return 'gray';
      case InvitationStatus.deleted:
        return 'red';
    }
  }
}
