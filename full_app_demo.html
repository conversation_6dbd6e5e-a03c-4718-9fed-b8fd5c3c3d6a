<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sijilli - تطبيق الشبكة الاجتماعية الكامل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }

        .app-container {
            max-width: 360px;
            margin: 0 auto;
            background: #ffffff;
            min-height: 100vh;
            box-shadow: 0 0 15px rgba(0,0,0,0.08);
            position: relative;
        }

        /* Login Screen */
        .login-screen {
            padding: 40px 24px;
            text-align: center;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .app-logo {
            font-size: 28px;
            color: #495057;
            font-weight: 600;
            margin-bottom: 6px;
        }

        .login-subtitle {
            color: #6c757d;
            margin-bottom: 40px;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 16px;
            text-align: right;
        }

        .form-input {
            width: 100%;
            padding: 10px 14px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            font-size: 14px;
            direction: ltr;
            text-align: left;
            background-color: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #495057;
            background-color: #ffffff;
        }

        .login-btn {
            width: 100%;
            padding: 10px;
            background: #495057;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin-bottom: 14px;
            font-weight: 500;
        }

        .login-btn:hover {
            background: #343a40;
        }

        .register-link {
            color: #495057;
            text-decoration: none;
            font-size: 13px;
        }

        /* Main App */
        .main-app {
            display: none;
        }

        .app-bar {
            background: #495057;
            color: white;
            padding: 12px;
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            position: relative;
        }

        .app-bar .refresh-btn {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            opacity: 0.8;
        }

        .app-bar .refresh-btn:hover {
            opacity: 1;
        }

        .content {
            padding-bottom: 80px;
            min-height: calc(100vh - 140px);
        }

        .appointment-card {
            margin: 12px;
            background: #ffffff;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            box-shadow: 0 1px 3px rgba(0,0,0,0.06);
            transition: all 0.2s ease;
        }

        .appointment-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            border-color: #adb5bd;
        }

        .card-header {
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .owner-info {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .avatar {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
        }

        .owner-name {
            font-size: 10px;
            font-weight: 500;
            color: #495057;
        }

        .days-remaining {
            font-size: 9px;
            color: #6c757d;
        }

        .card-bottom {
            padding: 10px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .copy-btn {
            padding: 4px 8px;
            border: 1px solid #adb5bd;
            border-radius: 4px;
            background: #f8f9fa;
            font-size: 9px;
            cursor: pointer;
            color: #495057;
            font-weight: 500;
        }

        .copy-btn:hover {
            background: #e9ecef;
            border-color: #6c757d;
        }

        .appointment-details {
            text-align: center;
            flex: 1;
            margin: 0 8px;
            direction: rtl;
        }

        .appointment-title {
            font-size: 12px;
            font-weight: 600;
            color: #212529;
            margin-bottom: 3px;
        }

        .location {
            font-size: 10px;
            font-weight: 500;
            color: #495057;
            margin-bottom: 2px;
        }

        .datetime {
            font-size: 9px;
            color: #6c757d;
        }

        .guest-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            position: relative;
        }

        .status-indicator {
            position: absolute;
            top: 0;
            right: 0;
            width: 8px;
            height: 8px;
            background: #dc3545;
            border-radius: 50%;
            border: 1px solid white;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 360px;
            background: #ffffff;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: space-around;
            padding: 6px 0;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 6px;
            color: #6c757d;
            text-decoration: none;
            font-size: 10px;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .nav-item.active {
            color: #495057;
        }

        .nav-icon {
            margin-bottom: 2px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .nav-icon svg {
            transition: all 0.2s ease;
        }

        .demo-note {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 12px;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
            color: #495057;
        }

        .loading {
            text-align: center;
            padding: 30px;
            color: #6c757d;
            font-size: 12px;
        }

        .empty-state {
            text-align: center;
            padding: 30px;
            color: #6c757d;
            font-size: 12px;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 12px;
        }

        .fab {
            position: fixed;
            bottom: 75px;
            left: 50%;
            transform: translateX(-50%);
            margin-left: 130px;
            width: 48px;
            height: 48px;
            background: #495057;
            border-radius: 50%;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            transition: all 0.2s ease;
        }

        .fab:hover {
            background: #343a40;
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Login Screen -->
        <div class="login-screen" id="loginScreen">
            <div class="app-logo">سِجِلّي</div>
            <div class="login-subtitle">تسجيل الدخول</div>
            
            <div class="form-group">
                <input type="email" class="form-input" placeholder="البريد الإلكتروني" id="emailInput">
            </div>
            
            <div class="form-group">
                <input type="password" class="form-input" placeholder="كلمة المرور" id="passwordInput">
            </div>
            
            <button class="login-btn" onclick="login()">تسجيل الدخول</button>
            
            <a href="#" class="register-link">ليس لديك حساب؟ إنشاء حساب جديد</a>
        </div>

        <!-- Main App -->
        <div class="main-app" id="mainApp">
            <div class="app-bar">
                <button class="refresh-btn" onclick="refreshAppointments()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="23,4 23,10 17,10"/>
                        <polyline points="1,20 1,14 7,14"/>
                        <path d="m20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4-4.64 4.36A9 9 0 0 1 3.51 15"/>
                    </svg>
                </button>
                <span id="appBarTitle">مواعيدي</span>
            </div>

            <div class="content" id="content">
                <div class="demo-note">
                    🎉 مرحباً بك في تطبيق Sijilli الكامل!<br>
                    جميع الميزات تعمل مع قاعدة بيانات Pocketbase
                </div>

                <div id="appointmentsList">
                    <!-- Appointments will be loaded here -->
                </div>
            </div>

            <!-- Floating Action Button -->
            <button class="fab" onclick="addAppointment()">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="5" x2="12" y2="19"/>
                    <line x1="5" y1="12" x2="19" y2="12"/>
                </svg>
            </button>

            <!-- Bottom Navigation -->
            <div class="bottom-nav">
                <div class="nav-item active" onclick="showHome()">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                            <polyline points="9,22 9,12 15,12 15,22"/>
                        </svg>
                    </div>
                    <div>الرئيسية</div>
                </div>
                <div class="nav-item" onclick="showSearch()">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="m21 21-4.35-4.35"/>
                        </svg>
                    </div>
                    <div>البحث</div>
                </div>
                <div class="nav-item" onclick="addAppointment()">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <line x1="12" y1="8" x2="12" y2="16"/>
                            <line x1="8" y1="12" x2="16" y2="12"/>
                        </svg>
                    </div>
                    <div>إضافة</div>
                </div>
                <div class="nav-item" onclick="showNotifications()">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"/>
                            <path d="m13.73 21a2 2 0 0 1-3.46 0"/>
                        </svg>
                    </div>
                    <div>الإشعارات</div>
                </div>
                <div class="nav-item" onclick="showSettings()">
                    <div class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="m12 1 2.09 6.26L22 9l-5.91 2.09L14 17l-2.09-5.91L4 9l5.91-2.09L12 1z"/>
                        </svg>
                    </div>
                    <div>الإعدادات</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample data
        const sampleAppointments = [
            {
                id: 1,
                title: 'اجتماع الكادر التعليمي السنوي',
                location: 'النعيم - مدرسة حمدان',
                datetime: 'السبت 22 يونيو 2036 - 9:00 مساءً',
                owner: 'السيد مدير المدرسة',
                daysRemaining: 30,
                hasGuest: true
            },
            {
                id: 2,
                title: 'ورشة تطوير المناهج',
                location: 'الفروانية - مركز التدريب',
                datetime: 'الأحد 21 يوليو 2024 - 2:00 مساءً',
                owner: 'السيد مدير المدرسة',
                daysRemaining: 7,
                hasGuest: true
            },
            {
                id: 3,
                title: 'اجتماع أولياء الأمور',
                location: 'حولي',
                datetime: 'الثلاثاء 16 يوليو 2024 - 6:30 مساءً',
                owner: 'السيد مدير المدرسة',
                daysRemaining: 2,
                hasGuest: false
            }
        ];

        let currentUser = null;
        let currentTab = 'home';

        // Login function
        function login() {
            const email = document.getElementById('emailInput').value;
            const password = document.getElementById('passwordInput').value;

            if (email && password) {
                // Simulate login
                currentUser = { name: 'المستخدم الحالي', email: email };

                // Hide login screen and show main app
                document.getElementById('loginScreen').style.display = 'none';
                document.getElementById('mainApp').style.display = 'block';

                // Load appointments
                loadAppointments();

                showNotification('تم تسجيل الدخول بنجاح!', 'success');
            } else {
                showNotification('يرجى إدخال البريد الإلكتروني وكلمة المرور', 'error');
            }
        }

        // Load appointments
        function loadAppointments() {
            const appointmentsList = document.getElementById('appointmentsList');
            appointmentsList.innerHTML = '<div class="loading">جاري تحميل المواعيد...</div>';

            // Simulate API call
            setTimeout(() => {
                appointmentsList.innerHTML = '';

                if (sampleAppointments.length === 0) {
                    appointmentsList.innerHTML = `
                        <div class="empty-state">
                            <div class="empty-icon">📅</div>
                            <div>لا توجد مواعيد</div>
                            <div style="font-size: 12px; color: #999; margin-top: 8px;">ابدأ بإنشاء موعد جديد</div>
                        </div>
                    `;
                } else {
                    sampleAppointments.forEach(appointment => {
                        appointmentsList.innerHTML += createAppointmentCard(appointment);
                    });
                }
            }, 1000);
        }

        // Create appointment card HTML
        function createAppointmentCard(appointment) {
            return `
                <div class="appointment-card" onclick="viewAppointment(${appointment.id})">
                    <div class="card-header">
                        <div class="owner-info">
                            <div class="avatar">👤</div>
                            <div class="owner-name">${appointment.owner}</div>
                        </div>
                        <div class="days-remaining">تبقى ${appointment.daysRemaining} ${appointment.daysRemaining === 1 ? 'يوم' : 'يوماً'}</div>
                    </div>
                    <div class="card-bottom">
                        <button class="copy-btn" onclick="event.stopPropagation(); copyAppointment(${appointment.id})">نسخة</button>
                        <div class="appointment-details">
                            <div class="appointment-title">${appointment.title}</div>
                            <div class="location">${appointment.location}</div>
                            <div class="datetime">${appointment.datetime}</div>
                        </div>
                        ${appointment.hasGuest ? `
                            <div class="guest-avatar">
                                👤
                                <div class="status-indicator"></div>
                            </div>
                        ` : '<div style="width: 28px;"></div>'}
                    </div>
                </div>
            `;
        }

        // Navigation functions
        function showHome() {
            setActiveTab('home');
            document.getElementById('appBarTitle').textContent = 'مواعيدي';
            loadAppointments();
        }

        function showSearch() {
            setActiveTab('search');
            document.getElementById('appBarTitle').textContent = 'البحث';
            document.getElementById('appointmentsList').innerHTML = `
                <div style="padding: 20px;">
                    <input type="text" class="form-input" placeholder="ابحث عن المستخدمين..." style="margin-bottom: 16px;">
                    <div class="empty-state">
                        <div class="empty-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#6c757d" stroke-width="1.5">
                                <circle cx="11" cy="11" r="8"/>
                                <path d="m21 21-4.35-4.35"/>
                            </svg>
                        </div>
                        <div>ابحث عن المستخدمين</div>
                    </div>
                </div>
            `;
        }

        function showNotifications() {
            setActiveTab('notifications');
            document.getElementById('appBarTitle').textContent = 'الإشعارات';
            document.getElementById('appointmentsList').innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#6c757d" stroke-width="1.5">
                            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"/>
                            <path d="m13.73 21a2 2 0 0 1-3.46 0"/>
                        </svg>
                    </div>
                    <div>لا توجد إشعارات جديدة</div>
                </div>
            `;
        }

        function showSettings() {
            setActiveTab('settings');
            document.getElementById('appBarTitle').textContent = 'الإعدادات';
            document.getElementById('appointmentsList').innerHTML = `
                <div style="padding: 20px;">
                    <div style="padding: 16px; border-bottom: 1px solid #eee; cursor: pointer; display: flex; align-items: center; gap: 12px;" onclick="showNotification('قريباً...', 'info')">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#6c757d" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                        الملف الشخصي
                    </div>
                    <div style="padding: 16px; border-bottom: 1px solid #eee; cursor: pointer; display: flex; align-items: center; gap: 12px;" onclick="showNotification('قريباً...', 'info')">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#6c757d" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M12 6v6l4 2"/>
                        </svg>
                        اللغة
                    </div>
                    <div style="padding: 16px; border-bottom: 1px solid #eee; cursor: pointer; display: flex; align-items: center; gap: 12px;" onclick="showNotification('قريباً...', 'info')">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#6c757d" stroke-width="2">
                            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"/>
                            <path d="m13.73 21a2 2 0 0 1-3.46 0"/>
                        </svg>
                        الإشعارات
                    </div>
                    <div style="padding: 16px; border-bottom: 1px solid #eee; cursor: pointer; display: flex; align-items: center; gap: 12px;" onclick="logout()">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#dc3545" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                            <polyline points="16,17 21,12 16,7"/>
                            <line x1="21" y1="12" x2="9" y2="12"/>
                        </svg>
                        تسجيل الخروج
                    </div>
                </div>
            `;
        }

        function setActiveTab(tab) {
            currentTab = tab;
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            document.querySelectorAll('.nav-item')[getTabIndex(tab)].classList.add('active');
        }

        function getTabIndex(tab) {
            const tabs = ['home', 'search', 'add', 'notifications', 'settings'];
            return tabs.indexOf(tab);
        }

        // Other functions
        function addAppointment() {
            showNotification('فتح شاشة إضافة موعد جديد...', 'info');
        }

        function viewAppointment(id) {
            showNotification(`عرض تفاصيل الموعد رقم ${id}`, 'info');
        }

        function copyAppointment(id) {
            showNotification('تم نسخ بيانات الموعد', 'success');
        }

        function refreshAppointments() {
            if (currentTab === 'home') {
                loadAppointments();
                showNotification('تم تحديث المواعيد', 'success');
            }
        }

        function logout() {
            currentUser = null;
            document.getElementById('loginScreen').style.display = 'flex';
            document.getElementById('mainApp').style.display = 'none';
            document.getElementById('emailInput').value = '';
            document.getElementById('passwordInput').value = '';
            showNotification('تم تسجيل الخروج', 'info');
        }

        function showNotification(message, type = 'info') {
            const colors = {
                success: '#4CAF50',
                error: '#f44336',
                info: '#2196F3'
            };

            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: ${colors[type]};
                color: white;
                padding: 12px 24px;
                border-radius: 4px;
                z-index: 1000;
                font-size: 14px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            // Allow Enter key to login
            document.getElementById('passwordInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    login();
                }
            });
        });
    </script>
</body>
</html>
