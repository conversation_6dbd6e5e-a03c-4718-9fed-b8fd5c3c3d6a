{"name": "profiles", "type": "base", "system": false, "schema": [{"name": "user", "type": "relation", "required": true, "unique": true, "options": {"collectionId": "users", "cascadeDelete": true, "minSelect": 1, "maxSelect": 1, "displayFields": ["username", "email"]}}, {"name": "display_name", "type": "text", "required": false, "unique": false, "options": {"min": 0, "max": 100, "pattern": ""}}, {"name": "bio", "type": "text", "required": false, "unique": false, "options": {"min": 0, "max": 500, "pattern": ""}}, {"name": "avatar", "type": "file", "required": false, "unique": false, "options": {"maxSelect": 1, "maxSize": 5242880, "mimeTypes": ["image/jpeg", "image/png", "image/gif", "image/webp"], "thumbs": ["100x100", "300x300"]}}, {"name": "phone", "type": "text", "required": false, "unique": false, "options": {"min": 0, "max": 20, "pattern": "^[+]?[0-9\\-\\s()]*$"}}, {"name": "location", "type": "text", "required": false, "unique": false, "options": {"min": 0, "max": 100, "pattern": ""}}, {"name": "birth_date", "type": "date", "required": false, "unique": false, "options": {"min": "", "max": ""}}, {"name": "gender", "type": "select", "required": false, "unique": false, "options": {"maxSelect": 1, "values": ["male", "female", "other", "prefer_not_to_say"]}}, {"name": "website", "type": "url", "required": false, "unique": false, "options": {"exceptDomains": [], "onlyDomains": []}}, {"name": "social_links", "type": "json", "required": false, "unique": false, "options": {}}, {"name": "privacy_settings", "type": "json", "required": false, "unique": false, "options": {}}, {"name": "notification_settings", "type": "json", "required": false, "unique": false, "options": {}}, {"name": "is_verified", "type": "bool", "required": false, "unique": false, "options": {}}, {"name": "is_public", "type": "bool", "required": false, "unique": false, "options": {}}], "indexes": ["CREATE UNIQUE INDEX idx_profiles_user ON profiles (user)", "CREATE INDEX idx_profiles_display_name ON profiles (display_name)", "CREATE INDEX idx_profiles_is_public ON profiles (is_public)"], "listRule": "@request.auth.id != \"\" && (is_public = true || user.id = @request.auth.id)", "viewRule": "@request.auth.id != \"\" && (is_public = true || user.id = @request.auth.id)", "createRule": "@request.auth.id != \"\" && user.id = @request.auth.id", "updateRule": "@request.auth.id != \"\" && user.id = @request.auth.id", "deleteRule": "@request.auth.id != \"\" && user.id = @request.auth.id", "options": {}}