# تعليمات تحديث قاعدة البيانات لتطبيق سجلي

## 🎯 التحديثات المطلوبة حسب المخطط النهائي:

### 1. جدول المستخدمين (users) ✅
```
- إضافة حقل isPublic (bool)
- role: user, approved, admin
```

### 2. جدول المواعيد (appointments) ⚠️ يحتاج تحديث
```
التغييرات المطلوبة:
- حذف حقل: datetime
- إضافة حقول:
  * calendarType (select: gregorian, hijri)
  * startDate (date) - مطلوب
  * endDate (date) - اختياري
  * hasTime (bool)
  * time (text) - للوقت
- تغيير privacy من (public/private) إلى (open/private)
- إضافة status (select: active, deleted, archived)
```

### 3. جدول حالات المدعوين (appointment_guests_status) ➕ جديد
```
- appointment (relation to appointments)
- user (relation to users)
- status (select: invited, accepted, rejected, removed)
- respondedAt (date)
- note (text, max 200)
```

### 4. جدول المتابعة (friendships) ➕ جديد
```
- sender (relation to users)
- receiver (relation to users)
- status (select: pending, accepted)
```

### 5. جدول الإشعارات (inbox) ⚠️ يحتاج تحديث
```
التغييرات:
- title (text)
- message (text)
- type (select: invitation, accept, decline, delete)
- owner (relation to users) - المستلم
- appointment (relation to appointments)
- relatedUser (relation to users) - المرسل
- read (bool)
```

### 6. جدول البروفايلات (profiles) ⚠️ يحتاج تحديث
```
الحقول المطلوبة:
- user (relation to users) - مطلوب، فريد
- avatar (file) - صورة شخصية
- displayName (text) - اسم العرض
- job (text) - الحرفة/المهنة
- phone (text) - رقم الهاتف
- bio (text) - النبذة الشخصية
- instagram (text) - اسم المستخدم في Instagram
- youtube (text) - اسم القناة في YouTube
```

## 🔧 خطوات التحديث في Pocketbase:

### الخطوة 1: تحديث جدول appointments
1. اذهب إلى Collections → appointments
2. أضف الحقول الجديدة:
   - **calendarType** (Select): gregorian, hijri
   - **startDate** (Date): مطلوب
   - **endDate** (Date): اختياري
   - **hasTime** (Bool): افتراضي true
   - **time** (Text): للوقت
   - **status** (Select): active, deleted, archived

3. عدل الحقول الموجودة:
   - **privacy** (Select): غير القيم من public/private إلى open/private

4. احذف الحقول القديمة:
   - **datetime** (بعد نقل البيانات)

### الخطوة 2: إنشاء جدول appointment_guests_status
1. اضغط "New collection" → "Base collection"
2. اسم الجدول: `appointment_guests_status`
3. أضف الحقول:
   - **appointment** (Relation): appointments collection
   - **user** (Relation): users collection
   - **status** (Select): invited, accepted, rejected, removed
   - **respondedAt** (Date): اختياري
   - **note** (Text): max 200, اختياري

### الخطوة 3: إنشاء جدول friendships
1. اضغط "New collection" → "Base collection"
2. اسم الجدول: `friendships`
3. أضف الحقول:
   - **sender** (Relation): users collection
   - **receiver** (Relation): users collection
   - **status** (Select): pending, accepted

### الخطوة 4: تحديث جدول inbox
1. اذهب إلى Collections → inbox
2. أضف/عدل الحقول:
   - **title** (Text): عنوان الإشعار
   - **message** (Text): محتوى الإشعار
   - **type** (Select): invitation, accept, decline, delete
   - **owner** (Relation): users - المستلم
   - **relatedUser** (Relation): users - المرسل

### الخطوة 5: تحديث جدول users
1. اذهب إلى Collections → users
2. أضف حقل:
   - **isPublic** (Bool): افتراضي true

### الخطوة 6: تحديث جدول profiles
1. اذهب إلى Collections → profiles (أو أنشئه إذا لم يكن موجود)
2. أضف/عدل الحقول:
   - **user** (Relation): users collection - مطلوب، فريد
   - **avatar** (File): صورة شخصية - اختياري
   - **displayName** (Text): اسم العرض - اختياري، max 50
   - **job** (Text): الحرفة/المهنة - اختياري، max 100
   - **phone** (Text): رقم الهاتف - اختياري، max 20
   - **bio** (Text): النبذة الشخصية - اختياري، max 500
   - **instagram** (Text): Instagram username - اختياري، max 50
   - **youtube** (Text): YouTube channel - اختياري، max 50

## 📋 قواعد الوصول المحدثة:

### appointments:
```
List: @request.auth.id != "" && (owner.id = @request.auth.id || guests.id ?= @request.auth.id) && status = "active"
View: @request.auth.id != "" && (owner.id = @request.auth.id || guests.id ?= @request.auth.id)
Create: @request.auth.id != ""
Update: @request.auth.id != "" && owner.id = @request.auth.id
Delete: @request.auth.id != "" && owner.id = @request.auth.id
```

### appointment_guests_status:
```
List: @request.auth.id != "" && user.id = @request.auth.id
View: @request.auth.id != "" && user.id = @request.auth.id
Create: @request.auth.id != "" && appointment.owner.id = @request.auth.id
Update: @request.auth.id != "" && user.id = @request.auth.id
Delete: @request.auth.id != "" && appointment.owner.id = @request.auth.id
```

### friendships:
```
List: @request.auth.id != "" && (sender.id = @request.auth.id || receiver.id = @request.auth.id)
View: @request.auth.id != "" && (sender.id = @request.auth.id || receiver.id = @request.auth.id)
Create: @request.auth.id != "" && sender.id = @request.auth.id
Update: @request.auth.id != "" && (sender.id = @request.auth.id || receiver.id = @request.auth.id)
Delete: @request.auth.id != "" && sender.id = @request.auth.id
```

### inbox:
```
List: @request.auth.id != "" && owner.id = @request.auth.id
View: @request.auth.id != "" && owner.id = @request.auth.id
Create: @request.auth.id != "" && relatedUser.id = @request.auth.id
Update: @request.auth.id != "" && owner.id = @request.auth.id
Delete: @request.auth.id != "" && owner.id = @request.auth.id
```

### profiles:
```
List: @request.auth.id != ""
View: @request.auth.id != ""
Create: @request.auth.id != "" && user.id = @request.auth.id
Update: @request.auth.id != "" && user.id = @request.auth.id
Delete: @request.auth.id != "" && user.id = @request.auth.id
```

## ✅ بعد التحديث:
- جرب التطبيق المحدث
- تأكد من عمل إنشاء المواعيد
- اختبر نظام الإشعارات
- تحقق من حالات المدعوين

## 🚀 الميزات الجديدة المتاحة:
- ✅ نظام حالات المدعوين المتقدم
- ✅ دعم التقويم الهجري والميلادي
- ✅ نظام المتابعة والصداقة
- ✅ إشعارات محسنة مع تفاصيل أكثر
- ✅ إدارة أفضل للمواعيد (نشط/محذوف/مؤرشف)
