-- إنشاء جدول البروفايل (profiles) في Pocketbase
-- يجب تنفيذ هذا في واجهة إدارة Pocketbase

-- جدول البروفايل
CREATE TABLE profiles (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL UNIQUE, -- ربط مع جدول المستخدمين
    display_name TEXT, -- الاسم المعروض (اختياري)
    bio TEXT, -- نبذة شخصية
    avatar_url TEXT, -- رابط الصورة الشخصية
    phone TEXT, -- رقم الهاتف
    location TEXT, -- الموقع
    birth_date TEXT, -- تاريخ الميلاد
    gender TEXT, -- الجنس
    website TEXT, -- الموقع الشخصي
    social_links JSON, -- روابط وسائل التواصل الاجتماعي
    privacy_settings JSON, -- إعدادات الخصوصية
    notification_settings JSON, -- إعدادات الإشعارات
    is_verified BOOLEAN DEFAULT FALSE, -- حالة التحقق
    is_public BOOLEAN DEFAULT TRUE, -- هل البروفايل عام
    created DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Key
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- فهارس لتحسين الأداء
CREATE INDEX idx_profiles_user_id ON profiles(user_id);
CREATE INDEX idx_profiles_display_name ON profiles(display_name);
CREATE INDEX idx_profiles_is_public ON profiles(is_public);

-- بيانات تجريبية
INSERT INTO profiles (id, user_id, display_name, bio, phone, location, is_verified, is_public) VALUES
('profile_1', 'user_1', 'أحمد محمد', 'مطور تطبيقات ومهتم بالتكنولوجيا', '+965-12345678', 'الكويت', TRUE, TRUE),
('profile_2', 'user_2', 'فاطمة علي', 'مصممة جرافيك ومطورة واجهات', '+965-87654321', 'الكويت', FALSE, TRUE),
('profile_3', 'user_3', 'محمد سالم', 'مدير مشاريع تقنية', '+965-11223344', 'الكويت', TRUE, TRUE);
