import 'package:flutter/material.dart';
import '../../constants/app_theme.dart';
import '../../services/pocketbase_service.dart';
import '../../models/models.dart';
import '../../widgets/appointment_card.dart';
import '../../utils/sample_data.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<Appointment> _appointments = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  Future<void> _loadAppointments() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final currentUser = PocketbaseService.instance.currentUser;
      if (currentUser != null) {
        final appointments = await PocketbaseService.instance.getUserAppointments(currentUser.id);
        setState(() {
          _appointments = appointments;
          _isLoading = false;
        });
      } else {
        // Show sample data when not authenticated (for demo purposes)
        setState(() {
          _appointments = SampleData.sampleAppointments;
          _isLoading = false;
        });
      }
    } catch (e) {
      // Show sample data on error (for demo purposes)
      setState(() {
        _appointments = SampleData.sampleAppointments;
        _error = null; // Don't show error, show sample data instead
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مواعيدي'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAppointments,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadAppointments,
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppTheme.errorColor,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل المواعيد',
              style: AppTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadAppointments,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_appointments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.event_note_outlined,
              size: 64,
              color: AppTheme.textHint,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مواعيد',
              style: AppTheme.headlineSmall.copyWith(color: AppTheme.textSecondary),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإنشاء موعد جديد',
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.textHint),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: _appointments.length,
      itemBuilder: (context, index) {
        final appointment = _appointments[index];
        return AppointmentCard(
          appointment: appointment,
          onTap: () => _navigateToAppointmentDetails(appointment),
          onCopy: () => _copyAppointmentData(appointment),
          onDelete: () => _deleteAppointment(appointment),
        );
      },
    );
  }

  void _navigateToAppointmentDetails(Appointment appointment) {
    // TODO: Navigate to appointment details
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل: ${appointment.title}')),
    );
  }



  void _copyAppointmentData(Appointment appointment) {
    // TODO: Implement copy functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم نسخ بيانات الموعد')),
    );
  }

  Future<void> _deleteAppointment(Appointment appointment) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الموعد'),
        content: const Text('هل أنت متأكد من حذف هذا الموعد؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.errorColor),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final success = await PocketbaseService.instance.deleteAppointment(appointment.id);
        if (success) {
          _loadAppointments();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم حذف الموعد بنجاح')),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف الموعد: $e')),
          );
        }
      }
    }
  }
}
