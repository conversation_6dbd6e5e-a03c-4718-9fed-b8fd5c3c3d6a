/// <reference path="../pb_data/types.d.ts" />
migrate((app) => {
  const collection = app.findCollectionByNameOrId("pbc_1037645436")

  // add field
  collection.fields.addAt(14, new Field({
    "hidden": false,
    "id": "json51210418",
    "maxSize": 0,
    "name": "primary_date",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "json"
  }))

  // add field
  collection.fields.addAt(15, new Field({
    "hidden": false,
    "id": "date883334011",
    "max": "",
    "min": "",
    "name": "gregorian_date",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "date"
  }))

  // update field
  collection.fields.addAt(5, new Field({
    "hidden": false,
    "id": "select2682684254",
    "maxSelect": 1,
    "name": "calendar_type",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "select",
    "values": [
      "gregorian",
      "hijri"
    ]
  }))

  return app.save(collection)
}, (app) => {
  const collection = app.findCollectionByNameOrId("pbc_1037645436")

  // remove field
  collection.fields.removeById("json51210418")

  // remove field
  collection.fields.removeById("date883334011")

  // update field
  collection.fields.addAt(5, new Field({
    "hidden": false,
    "id": "select2682684254",
    "maxSelect": 1,
    "name": "calendarType",
    "presentable": false,
    "required": false,
    "system": false,
    "type": "select",
    "values": [
      "gregorian",
      "hijri"
    ]
  }))

  return app.save(collection)
})
